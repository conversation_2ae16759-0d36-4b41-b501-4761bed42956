# Тест функциональности ModalSettings

## Описание изменений

Добавлена логика условного отображения настройки "Отображать квитирование ПГ" в компоненте ModalSettings.

## Условия отображения

Настройка отображается только если выполняются ОБА условия:

1. **Роль пользователя**: Пользователь должен иметь роль "engineer" (Технолог)
2. **Режим работы**: Должна быть включена Централизованная часть (ИА, ОДУ Востока)
   - `isCenter` (из AuthStore) должно быть `true`
   - `isModeCenter` (из getMode.ts) должно быть `true`

## Логика работы

### Загрузка настроек
- При загрузке компонента проверяются условия доступности
- Если настройка доступна - загружается с сервера через `getTableParams`
- Если недоступна - устанавливается значение `false`

### Сохранение настроек
- При сохранении настройка отправляется на сервер только если она доступна пользователю
- Если недоступна - пропускается при сохранении

### Отображение в UI
- Чекбокс "Отображать квитирование ПГ" отображается только при соблюдении условий
- Используется условный рендеринг `{shouldShowPgAckSetting() && (...)}`

## Тестовые сценарии

### Сценарий 1: Технолог в Централизованной части
- Роль: "engineer"
- isCenter: true
- isModeCenter: true
- **Ожидаемый результат**: Настройка отображается и работает

### Сценарий 2: Технолог в Распределенной части
- Роль: "engineer"
- isCenter: false
- isModeCenter: true
- **Ожидаемый результат**: Настройка НЕ отображается

### Сценарий 3: Не технолог в Централизованной части
- Роль: "viewer" или другая
- isCenter: true
- isModeCenter: true
- **Ожидаемый результат**: Настройка НЕ отображается

### Сценарий 4: Технолог в режиме НЕ center
- Роль: "engineer"
- isCenter: true
- isModeCenter: false
- **Ожидаемый результат**: Настройка НЕ отображается

## Файлы изменены

- `src/routes/components/ModalSettings/ModalSettings.tsx`
  - Добавлены импорты: useStores, observer, isModeCenter, Roles
  - Добавлена функция shouldShowPgAckSetting с useCallback
  - Обновлена логика загрузки и сохранения настроек
  - Добавлен условный рендеринг для чекбокса
