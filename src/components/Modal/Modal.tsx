import { Loader } from "components/Loader";
import styled from "styled-components";
import React, { FC, useEffect, useRef, useState } from "react";
import { CloseIcon, Container, ModalContainer, Body, Title, Description, CancelButton, ConfirmButton, HintContainer, IconContainer, TrashIcon } from "./Modal.style";
import { ModalProps } from "./Modal.types";

export const Header = styled.div`
  padding: 28px;
  border-bottom: 1px solid #e5e5e5;
  display: flex;
  cursor: move;
  flex-shrink: 0;
`;

export const Footer = styled.div`
  width: 100%;
  padding: 15px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-shrink: 0;
`;

export const LeftHeader = styled.div`
  height: 16px;
  width: 95%;
`;
export const RightHeader = styled.div`
  width: 5%;
  height: 16px;
`;

export const TopLine = styled.div`
  cursor: n-resize;
  height: 7px;
  width: 100%;
  top: -5px;
  left: 0;
  position: absolute;
`;

export const RightLine = styled.div`
  cursor: e-resize;
  width: 7px;
  height: 100%;
  right: -5px;
  top: 0;
  position: absolute;
`;

export const BottomLine = styled.div`
  cursor: s-resize;
  height: 7px;
  width: 100%;
  bottom: -5px;
  left: 0;
  position: absolute;
`;

export const LeftLine = styled.div`
  cursor: w-resize;
  width: 7px;
  position: absolute;
  left: -5px;
  top: 0;
  height: 100%;
`;

export const RightTopPoint = styled.div`
  cursor: ne-resize;
  width: 9px;
  height: 9px;
  right: -5px;
  top: -5px;
  position: absolute;
`;

export const RightBottomPoint = styled.div`
  cursor: se-resize;
  width: 12px;
  height: 12px;
  right: 1px;
  bottom: 1px;
  position: absolute;
`;

export const LeftBottomPoint = styled.div`
  cursor: sw-resize;
  width: 9px;
  height: 9px;
  left: -5px;
  bottom: -5px;
  position: absolute;
`;

export const LeftTopPoint = styled.div`
  cursor: nw-resize;
  width: 9px;
  height: 9px;
  left: -5px;
  top: -5px;
  position: absolute;
`;

export const getPoints = (word: string): number => {
  const key = word
    .split("")
    .filter((el) => el !== "p")
    .filter((el) => el !== "x")
    .join("");
  return Number(key) ? Number(key) : 0;
};

export const Modal: FC<ModalProps> = (props) => {
  const {
    isOverLay,
    children,
    cancelText,
    confirmText,
    title,
    description,
    hint,
    colorScheme = "default",
    icon,
    onCancel,
    onConfirm,
    className,
    isDisabledConfirm = false,
    isDisabledCancel = false,
    isLoading = false,
    widthButtons = 160,
    width,
    height,
    setModalHeight = null,
    dataTestContainer,
    dataTestConfirmButton,
    scrollableContent = false,
    hideCloseIcon = false,
  } = props;
  const refModal = useRef<any>(null);

  useEffect(() => {
    if (refModal?.current) {
      const x = String((Number(window.innerWidth) - Number(width)) / 2);
      const y = String((Number(window.innerHeight) - Number(height)) / 2);
      refModal.current.style.left = `${x}px`;
      refModal.current.style.top = `${y}px`;
    }
  }, [refModal]);

  const [deltaModal, setDeltaModal] = useState<any>({ x: "0", y: "0" });

  const [resizeMode, setResizeMode] = useState(null);

  const onMouseDown = (e: any) => {
    if (!!e.target.dataset.resize) {
      setResizeMode(e.target.dataset.resize);
      const x = getPoints(refModal.current.style.left);
      const y = getPoints(refModal.current.style.top);
      if (e.target.dataset.resize === "header") {
        setDeltaModal({ x: String(Number(x) - Number(e.pageX)), y: String(Number(y) - Number(e.pageY)) });
      } else {
        const widthModal = refModal.current.style.width ? getPoints(refModal.current.style.width) : width;
        const heightModal = refModal.current.style.height ? getPoints(refModal.current.style.height) : height;
        setDeltaModal({ x, y, widthModal, heightModal });
      }
    }
  };
  const onMouseUp = () => {
    if (!!resizeMode) {
      setResizeMode(null);
      setDeltaModal({ x: "0", y: "0" });
    }
  };
  const onMouseMove = (e: any) => {
    if (!!resizeMode) {
      if (resizeMode === "right-line") {
        const { x } = deltaModal;
        const tempWidth = Number(e.pageX) - Number(x);
        const finalWidth = tempWidth < width ? width : tempWidth;
        refModal.current.style.width = `${finalWidth}px`;
      }
      if (resizeMode === "left-line") {
        const { x, widthModal } = deltaModal;
        const tempWidth = Number(x) - Number(e.pageX) + widthModal;
        const finalWidth = tempWidth < width ? width : tempWidth;
        refModal.current.style.width = `${finalWidth}px`;
        const isMove = tempWidth >= width;
        if (isMove) {
          refModal.current.style.left = `${e.pageX}px`;
        }
      }
      if (resizeMode === "top-line") {
        const { y } = deltaModal;
        const tempHeight = Number(y) - Number(e.pageY) + height;
        const finalHeight = tempHeight < height ? height : tempHeight;
        refModal.current.style.height = `${finalHeight}px`;
        const isMove = tempHeight >= height;
        if (isMove) {
          refModal.current.style.top = `${e.pageY}px`;
        }
        if (setModalHeight) {
          setModalHeight(finalHeight);
        }
      }
      if (resizeMode === "bottom-line") {
        const { y } = deltaModal;
        const tempHeight = Number(e.pageY) - Number(y);
        const finalHeight = tempHeight < height ? height : tempHeight;
        refModal.current.style.height = `${finalHeight}px`;
        if (setModalHeight) {
          setModalHeight(finalHeight);
        }
      }
      if (resizeMode === "right-top-point") {
        const { x, y } = deltaModal;
        const tempWidth = Number(e.pageX) - Number(x);
        const finalWidth = tempWidth < width ? width : tempWidth;
        refModal.current.style.width = `${finalWidth}px`;
        const tempHeight = Number(y) - Number(e.pageY) + height;
        const finalHeight = tempHeight < height ? height : tempHeight;
        refModal.current.style.height = `${finalHeight}px`;
        const isMove = tempHeight >= height;
        if (isMove) {
          refModal.current.style.top = `${e.pageY}px`;
        }
        if (setModalHeight) {
          setModalHeight(finalHeight);
        }
      }
      if (resizeMode === "right-bottom-point") {
        const { x } = deltaModal;
        const tempWidth = Number(e.pageX) - Number(x);
        const finalWidth = tempWidth < width ? width : tempWidth;
        refModal.current.style.width = `${finalWidth}px`;
        const { y } = deltaModal;
        const tempHeight = Number(e.pageY) - Number(y);
        const finalHeight = tempHeight < height ? height : tempHeight;
        refModal.current.style.height = `${finalHeight}px`;
        if (setModalHeight) {
          setModalHeight(finalHeight);
        }
      }
      if (resizeMode === "left-bottom-point") {
        const { x, widthModal, y } = deltaModal;
        const tempWidth = Number(x) - Number(e.pageX) + widthModal;
        const finalWidth = tempWidth < width ? width : tempWidth;
        refModal.current.style.width = `${finalWidth}px`;
        const isMove = tempWidth >= width;
        if (isMove) {
          refModal.current.style.left = `${e.pageX}px`;
        }
        const tempHeight = Number(e.pageY) - Number(y);
        const finalHeight = tempHeight < height ? height : tempHeight;
        refModal.current.style.height = `${finalHeight}px`;
        if (setModalHeight) {
          setModalHeight(finalHeight);
        }
      }
      if (resizeMode === "left-top-point") {
        const { x, widthModal, y } = deltaModal;
        const tempWidth = Number(x) - Number(e.pageX) + widthModal;
        const finalWidth = tempWidth < width ? width : tempWidth;
        refModal.current.style.width = `${finalWidth}px`;
        const isMoveX = tempWidth >= width;
        if (isMoveX) {
          refModal.current.style.left = `${e.pageX}px`;
        }
        const tempHeight = Number(y) - Number(e.pageY) + height;
        const finalHeight = tempHeight < height ? height : tempHeight;
        refModal.current.style.height = `${finalHeight}px`;
        const isMoveY = tempHeight >= height;
        if (isMoveY) {
          refModal.current.style.top = `${e.pageY}px`;
        }
        if (setModalHeight) {
          setModalHeight(finalHeight);
        }
      }
      if (resizeMode === "header") {
        const xAny = String(Number(e.clientX) + Number(deltaModal.x));
        const yAny = String(Number(e.clientY) + Number(deltaModal.y));
        refModal.current.style.left = `${xAny}px`;
        refModal.current.style.top = `${yAny}px`;
      }
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", onMouseDown);
    document.addEventListener("mouseup", onMouseUp);
    document.addEventListener("mousemove", onMouseMove);
    return () => {
      document.removeEventListener("mousedown", onMouseDown);
      document.removeEventListener("mouseup", onMouseUp);
      document.removeEventListener("mousemove", onMouseMove);
    };
  }, [refModal, resizeMode, deltaModal]);

  return (
    <ModalContainer
      id="modalContainer"
      ref={refModal}
      className={className}
      width={width}
      height={height}
      data-test={dataTestContainer}
      scrollableContent={scrollableContent}
    >
      <Header data-resize="header">
        <LeftHeader>
          {title && (
            <Title data-resize="header">
              {icon && (
                <IconContainer colorScheme={colorScheme}>
                  <TrashIcon colorScheme={colorScheme} width={20} name={icon} />
                </IconContainer>
              )}
              <>{title}</>
            </Title>
          )}
          {description && <Description data-resize="header">{description}</Description>}
          {hint && (
            <HintContainer colorScheme={colorScheme} data-resize="header">
              {hint}
            </HintContainer>
          )}
        </LeftHeader>
        {!hideCloseIcon && (
          <RightHeader>
            <CloseIcon
              name="close"
              width={16}
              onClick={() => {
                if (onCancel) {
                  onCancel();
                }
              }}
            />
          </RightHeader>
        )}
      </Header>
      <Body scrollableContent={scrollableContent}>{children && children}</Body>
      <TopLine data-resize="top-line" />
      <RightLine data-resize="right-line" />
      <BottomLine data-resize="bottom-line" />
      <LeftLine data-resize="left-line" />
      <RightTopPoint data-resize="right-top-point" />
      <RightBottomPoint data-resize="right-bottom-point" />
      <LeftBottomPoint data-resize="left-bottom-point" />
      <LeftTopPoint data-resize="left-top-point" />
      {(onConfirm || onCancel) && (
        <Footer>
          {onConfirm && confirmText && (
            <ConfirmButton
              colorScheme={colorScheme}
              title={confirmText}
              disabled={isDisabledConfirm}
              width={widthButtons}
              dataTest={dataTestConfirmButton}
              onClick={() => {
                if (onConfirm) {
                  onConfirm();
                }
              }}
            />
          )}
          {onCancel && cancelText && (
            <CancelButton
              colorScheme={colorScheme}
              title={cancelText}
              disabled={isDisabledCancel}
              width={widthButtons}
              onClick={() => {
                if (onCancel) {
                  onCancel();
                }
              }}
            />
          )}
        </Footer>
      )}
    </ModalContainer>
  );
};
