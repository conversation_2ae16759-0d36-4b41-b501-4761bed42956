import {
  image1,
  image2,
  image3,
  image4,
  image5,
  image6,
  image7,
  image8,
  image9,
  image10,
  image11,
  image12,
  image13,
  image14,
  image15,
  image16,
  image17,
  image18,
  image19,
  image20,
  image21,
  image22,
  image23,
  image24,
  image25,
  image26,
  image27,
  image28,
  image29,
  image30,
  image31,
  image32,
  image33,
  image34,
  image35,
  image36,
  image37,
  image38,
  image39,
  image40,
  image41,
  image42,
  image43,
  image44,
  image45,
  image46,
  image47,
  image48,
  image49,
  image50,
  image51,
  image52,
  image53,
  image54,
  image55,
  image56,
  image57,
  image58,
  image59,
  image60,
  image61,
  image62,
  image63,
  image64,
  image65,
  image66,
  image67,
  image68,
  image69,
  image70,
  image71,
  image72,
  image73,
  image74,
  image75,
  image76,
  image77,
  image78,
  image79,
  image80,
  image81,
  image82,
  image83,
  image84,
  image85,
  image86,
  image87,
  image88,
  image89,
  image90,
  image91,
  image92,
  image93,
  image94,
  image95,
  image96,
  image97,
  image98,
  image99,
  image100,
  image101,
  image102,
  image103,
  image104,
  image105,
  image106,
  image107,
  image108,
  image109,
  image110,
  image111,
  image112,
  image113,
  image114,
  image115,
  image116,
  image117,
  image118,
  image119,
  image120,
  image121,
  image122,
  image123,
  image124,
  image125,
  image126,
  image127,
  image128,
  image129,
  image130,
  image131,
  image132,
  image133,
  image134,
  image135,
  image136,
  image137,
  image138,
  image139,
  image140,
  image141,
  image142,
  image143,
  image144,
  image145,
  image146,
  image147,
  image148,
  image149,
  image150,
  image151,
  image152,
  image153,
  image154,
  image155,
  image156,
  image157,
  image158,
  image159,
  image160,
  image161,
  image162,
  image163,
  image164,
  image165,
  image166,
  image167,
  image168,
  image169,
  image170,
  image171,
  image172,
  image173,
  image174,
  image175,
  image176,
  image177,
  image178,
  image179,
  image180,
  image181,
  image182,
  image183,
  image184,
  image185,
  image186,
  image187,
  image188,
  image189,
  image190,
  image191,
  image192,
  image193,
  image194,
  image195,
  image196,
  image197,
  image198,
  image199,
  image200,
  image201,
  image202,
  image203,
  image204,
  image205,
  image206,
  image207,
  image208,
  image209,
  image210,
  image211,
  image212,
  image213,
  image214,
  image215,
  image216,
  image217,
  image218,
  image219,
  image220,
  image221,
  image222,
  image223,
  image224,
  image225,
  image226,
  image227,
  image228,
  image229,
  image230,
  image231,
  image232,
  image233,
  image234,
  image235,
  image236,
  image237,
  image238,
  image239,
  image240,
  image241,
  image242,
  image243,
  image244,
  image245,
  image246,
  image247,
  image248,
  image249,
  image250,
  image251,
  image252,
  image253,
  image254,
  image255,
  image256,
  image257,
  image258,
  image259,
  image260,
  image261,
  image262,
  image263,
  image264,
  image265,
  image266,
  image267,
  image268,
  image269,
  image270,
  image271,
  image272,
  image273,
  image274,
  image275,
  image276,
  image277,
  image278,
  image279,
  image280,
  image281,
  image282,
  image283,
  image284,
  image285,
  image286,
  image287,
  image288,
  image289,
  image290,
  image291,
  image292,
  image293,
  image294,
  image295,
  image296,
  image297,
  image298,
  image299,
  image300,
  image301,
  image302,
  image303,
  image304,
  image305,
  image306,
  image307,
  image308

} from "./images"
import { Icon } from "components/Icon";
import {
  Table,
  Title,
  TH,
  TitlePage,
  TextPage,
  TD,
  StatusText,
  DescriptionPage,
  Left,
  AltPicture,
  BigPicture,
  Right,
  LeftContent,
  Content,
  RightContent,
  Container,
  Page,
  Row,
  Circle,
  IconContainer,
  Status, NumericOl, NumericLi,
} from "./GuidePage.style";
import React from "react";

export const GuidePage = () => {
  const tableOfContents = [
    { title: "1. ОБЩИЕ ПОЛОЖЕНИЯ", type: "category", href: "#general_provisions" },
    { title: "1.1. Полное наименование системы и её условное обозначение", type: "subcategory", href: "#full_name_of_the_system_and_its_symbol" },
    { title: "1.2. Основные понятия, определения и сокращения", type: "subcategory", href: "#basic_concepts_definitions_and_abbreviations" },
    { title: "2. НАЗНАЧЕНИЕ И ФУНКЦИИ СИСТЕМЫ", type: "category", href: "#purpose_and_functions_of_the_system" },
    { title: "2.1. Назначение системы", type: "subcategory", href: "#purpose_of_the_system" },
    { title: "2.2. Описание Системы", type: "subcategory", href: "#description_of_the_system" },
    { title: "3. ИНТЕРФЕЙС ПОЛЬЗОВАТЕЛЯ", type: "category", href: "#user_interface" },
    { title: "3.1. Авторизация пользователей", type: "subcategory", href: "#user_authorization" },
    { title: "3.2. Работа с разделами в Централизованной части Системы (ИА, ОДУ Востока)", type: "subcategory", href: "#working_with_partitions_in_a_centralized" },
    { title: "3.2.1. Работа с НСИ", type: "subsubcategory", href: "#nsi" },
    { title: "3.2.2. Работа с Плановыми графиками", type: "subsubcategory", href: "#scheduled_schedules" },
    { title: "3.3. Работа с разделами в Распределенных части Системы (ОДУ, РДУ)", type: "subcategory", href: "#working_with_partitions_in_distributed" },
    { title: "3.3.2. Работа с НСИ", type: "subsubcategory", href: "#work_nsi" },
    { title: "3.3.3. Работа с Плановыми графиками", type: "subsubcategory", href: "#working_with_scheduled_schedules" },
    { title: "3.3.4. Уведомления о событиях", type: "subsubcategory", href: "#event_notifications" },
    { title: "3.4. Настройка пользовательского интерфейса", type: "subcategory", href: "#customizing_user_interface" },
    { title: "3.4.1. Изменение аватара пользователя", type: "subsubcategory", href: "#changing_the_users_avatar" },
    { title: "3.4.2. Изменение темы интерфейса", type: "subsubcategory", href: "#changing_interface_theme" },
    { title: "3.4.3. Отображение версии приложения", type: "subsubcategory", href: "#displaying_application_version" },
    { title: "3.4.4. Изменение вида выпадающих списков", type: "subsubcategory", href: "#changing_appearance_drop_down_lists" },
    { title: "3.4.5. Выход из системы", type: "subsubcategory", href: "#log_out_system" },
    { title: "4. АДМИНИСТРИРОВАНИЕ", type: "category", href: "#administration" },
    { title: "5. НАСТРОЙКИ СИСТЕМЫ", type: "category", href: "#system_settings" },
    { title: "5.1. Общие настройки ", type: "subcategory", href: "#general_settings" },
    { title: "5.1.1. Настройка глубины хранения данных ", type: "subsubcategory", href: "#setting_the_data_storage_depth" },
    { title: "5.1.2. Настройки взаимодействия с внешними системами ", type: "subsubcategory", href: "#settings_for_interaction_with_external_systems" },
    { title: "5.2. Настройки ПГ ", type: "subcategory", href: "#pg_settings" },
    { title: "5.2.1. Настройка Номер ПБР [1СЗ] ", type: "subsubcategory", href: "#pbr" },
    { title: "5.2.2. Настройка Номер ПБР [2СЗ] ", type: "subsubcategory", href: "#uddg" },
    { title: "5.2.3. Настройка характеристик ПГ ", type: "subsubcategory", href: "#setting_up_pg_characteristics" },
    { title: "5.2.4. Настройка константы отсутствия данных  ", type: "subsubcategory", href: "#empty_data" },
    { title: "5.2.5. Настройка загрузки ПГ  ", type: "subsubcategory", href: "#setting_up_pg_loading" },
    {
      title: "5.2.6. Настройка состава передаваемых данных графика в MODES-Terminal",
      type: "subsubcategory",
      href: "#configuring_the_composition_of_transmitted_graph_data_in_MODES_Terminal",
    },
    { title: "5.3. Распространение настроек в ДЦ", type: "subcategory", href: "#distribution_of_settings_in_DC" },
    { title: "6. ЖУРНАЛИРОВАНИЕ", type: "category", href: "#logging" },
  ];
  return (
    <Container>
      <Title>Справочная информация</Title>
      <Content>
        <LeftContent>
          {tableOfContents.map((el, index) => {
            return (
              <Row type={el.type} key={"row-1"} href={el.href}>
                <Circle type={el.type} />
                {el.title}
              </Row>
            );
          })}
        </LeftContent>
        <RightContent>
          <TitlePage name={`general_provisions`}>1. ОБЩИЕ ПОЛОЖЕНИЯ</TitlePage>
          <DescriptionPage name={`full_name_of_the_system_and_its_symbol`}>1.1. Полное наименование системы и её условное обозначение</DescriptionPage>
          <p>Наименование программы для ЭВМ: «Система распространения плановых графиков».
            Условное обозначение программы для ЭВМ: Система, СРПГ.</p>
          <DescriptionPage name={`basic_concepts_definitions_and_abbreviations`}>1.2. Основные понятия, определения и сокращения</DescriptionPage>
          <table>
            <tr>
              <td>
                <p>АО «СО ЕЭС» (СО)</p>
              </td>
              <td>
                <p>Акционерное общество «Системный оператор Единой энергетической системы»</p>
              </td>
            </tr>
            <tr>
              <td>
                <p>БД</p>
              </td>
              <td>
                <p>База данных</p>
              </td>
            </tr>
            <tr>
              <td>
                <p>ГОУ</p>
              </td>
              <td>
                <p>Групповой объект управления</p>
              </td>
            </tr>
            <tr>
              <td>
                <p>ДЦ</p>
              </td>
              <td>
                <p>Структурное подразделение АО «СО ЕЭС» – субъекта оперативно-диспетчерского управления в электроэнергетике, осуществляющее в пределах закрепленной за ним операционной зоны управление электроэнергетическим режимом энергосистемы (ИА, ОДУ, РДУ)</p>
              </td>
            </tr>
            <tr>
              <td>
                <p>ДДГ</p>
              </td>
              <td>
                <p>Доводимый диспетчерский график</p>
              </td>
            </tr>
            <tr>
              <td>
                <p>ЕЭС</p>
              </td>
              <td>
                <p>Единая Энергетическая Система</p>
              </td>
            </tr>
            <tr>
              <td>
                <p>ёЖ-3</p>
              </td>
              <td>
                <p>Подсистема «Электронный оперативный журнал ёЖ-3» ИУС «ОИК СК-11»</p>
              </td>
            </tr>
            <tr>
              <td>
                <p>ИА</p>
              </td>
              <td>
                <p>Исполнительный аппарат АО «СО ЕЭС»</p>
              </td>
            </tr>
            <tr><td><p>ИУС</p></td><td><p>Информационно-управляющая система</p></td></tr>
            <tr><td><p>НСИ</p></td><td><p>Нормативно-справочная информация</p></td></tr>
            <tr><td><p>ОДУ</p></td><td><p>Филиал АО «СО ЕЭС».Объединенное диспетчерское управление</p></td></tr>
            <tr><td><p>ОИК СК-11</p></td><td><p>Оперативно-информационный комплекс СК-11</p></td></tr>
            <tr><td><p>ПАК</p></td><td><p>Программно-аппаратный комплекс</p></td></tr>
            <tr><td><p>ПАК «ЕСМ»</p></td><td><p>ПАК «Единая Система Мониторинга»</p></td></tr>
            <tr><td><p>ПАК «MODES-Terminal»</p></td><td><p>ПАК «Система обмена уведомлениями о топологии сети и сетевых ограничениях», «Система обмена уведомлениями о составе и параметрах оборудования», «Обмен информацией с участниками рынка»</p></td></tr>
            <tr><td><p>ПАК «ЕСС»</p></td><td><p>Программно-аппаратный комплекс «Информационная система ведения реестров объектов, участвующих в рыночных приложениях»</p></td></tr>
            <tr><td><p>ПАК «ОпАМ»</p></td><td><p>ПАК «Оптимизация активной мощности»</p></td></tr>
            <tr><td><p>ПГ</p></td><td><p>Плановый график</p></td></tr>
            <tr><td><p>ПГ СК-11</p></td><td><p>Плановые графики в формате ОИК СК-11</p></td></tr>
            <tr><td><p>ПЭР</p></td><td><p>Предварительный электроэнергетический режим</p></td></tr>
            <tr><td><p>ПДГ</p></td>
              <td>
                <p>Прогнозный диспетчерский график</p>
              </td>
            </tr>
            <tr>
              <td>
                <p>ППБР </p>
              </td>
              <td>
                <p>Предварительный план балансирующего рынка</p>
              </td>
            </tr>
            <tr>
              <td>
                <p>ПГБР </p>
              </td>
              <td>
                <p>Прогнозный график балансирующего рынка</p>
              </td>
            </tr>
            <tr>
              <td>
                <p>ПБР </p>
              </td>
              <td>
                <p>План балансирующего рынка</p>
              </td>
            </tr>
            <tr>
              <td>
                <p>ПО </p>
              </td>
              <td>
                <p>Программное обеспечение</p>
              </td>
            </tr>
            <tr>
              <td>
                <p>РДУ </p>
              </td>
              <td>
                <p>Филиал АО «СО ЕЭС» Региональное диспетчерское управление</p>
              </td>
            </tr>
            <tr>
              <td>
                <p>СЗ </p>
              </td>
              <td>
                <p>Синхронная зона</p>
              </td>
            </tr>
            <tr>
              <td>
                <p>СРДК </p>
              </td>
              <td>
                <p>ИУС «Система регистрации диспетчерских команд»</p>
              </td>
            </tr>
            <tr>
              <td>
                <p>СРПГ, Система</p>
              </td>
              <td>
                <p>Информационно-управляющая система «Система распространения плановых графиков»</p>
              </td>
            </tr>
            <tr>
              <td>
                <p>УДДГ </p>
              </td>
              <td>
                <p>Уточненный доводимый диспетчерский график</p>
              </td>
            </tr>
            <tr>
              <td>
                <p>AD </p>
              </td>
              <td>
                <p>Служба каталогов, являющаяся единым хранилищем данных организации и контролирующая доступ для пользователей на основе политики безопасности каталога.</p>
              </td>
            </tr>
          </table>
          <Page className="block0">
            <TitlePage name={`purpose_and_functions_of_the_system`}>2. НАЗНАЧЕНИЕ И ФУНКЦИИ СИСТЕМЫ</TitlePage>
            <DescriptionPage name={`purpose_of_the_system`}>2.1.Назначение системы</DescriptionPage>
            <p>Система предназначена для гарантированной доставки плановых графиков, сформированных при краткосрочном планировании электроэнергетических режимов, в ОИК СК-11 и в ПАК «MODES-Terminal».</p>
            <DescriptionPage name={`description_of_the_system`}>2.2.Описание Системы</DescriptionPage>
          </Page>
          <p>Основные функции, выполняемые в Системе: </p>
          <ul type="disc">
            <li>1. В Централизованной части 1СЗ (расположена на уровне ИА) выполня-ются:
              <ul type="disc">
                <li>1.1 Обработка НСИ:
                <ul type="disc">
                  <li>1.1.1. Загрузка НСИ из ПАК «ЕСС».</li>
                  <li>1.1.2. Распространение НСИ в ДЦ.</li>
                  <li>1.1.3. Контроль распространения НСИ в ДЦ.</li>
                </ul>
                </li>
                <li>1.2. Обработка ПГ:
                  <ul type="disc">
                    <li>1.2.1. Загрузка (запрос, инициированный Системой, и синхронный ответ ПАК «ОпАМ») ПГ из ПАК «ОпАМ» ИА.</li>
                    <li>1.2.2. Загрузка ПГ из Мегаточки.</li>
                    <li>1.2.3. Преобразование ПГ в ПГ СК-11.</li>
                    <li>1.2.4. Распространение ПГ СК-11 и результатов обработки данных (п.1.3) в ДЦ.</li>
                    <li>1.2.5. Акцепт ПГ СК-11.</li>
                    <li>1.2.6. Получение и обработка квитанций об успешной/неуспешной пе-редаче и записи ПГ в ДЦ. </li>
                    <li>1.2.7. Контроль распространения ПГ СК-11 в ДЦ.</li>
                    <li>1.2.8. Контроль передачи ПГ СК-11 из ДЦ во внешние системы: ОИК СК-11, ПАК «MODES-Terminal».</li>
                  </ul>
                </li>
                <li>1.3. Обработка данных:
                  <ul type="disc">
                    <li>1.3.1. Расчет агрегированных параметров «сумматоры».</li>
                    <li>1.3.2. Расчет агрегированных параметров «ГОУ».</li>
                  </ul>
                </li>
                <li>1.4. Передача данных:
                  <ul type="disc">
                    <li>1.4.1. Передача признака акцепта ПГ в ПАК «ОпАМ» и Распределенные части Системы.</li>
                  </ul>
                </li>
              </ul>
            </li>
            <li>2. В Распределенных частях Системы 1СЗ (расположена на уровне ИА, ОДУ, РДУ):
              <ul type="disc">
                <li>2.1. Получение данных:
                  <ul type="disc">
                    <li>2.1.1. Получение НСИ из Централизованной части.</li>
                    <li>2.1.2. Получение ПГ СК-11 и результатов обработки данных (п.1.3) из Централизованной части.</li>
                    <li>2.1.3. Получение акцепта ПГ в ДЦ;</li>
                    <li>2.1.4. Получение от Централизованной части 2СЗ ПГ (ДДГ) и информации о его акцепте (для Распределенной части системы уровня ИА).</li>
                  </ul>
                </li>
                <li>2.2. Передача данных:
                  <ul type="disc">
                    <li>2.2.1. Настройка состава параметров, передаваемых в ОИК СК-11 ДЦ.</li>
                    <li>2.2.2. Передача ПГ СК-11 в ОИК СК-11 ДЦ.</li>
                    <li>2.2.3. Передача акцептованного ПГ СК-11 в ПАК «MODES-Terminal».</li>
                    <li>2.2.4. Передача акцептованного ПГ СК-11 в ПАК «СРДК».</li>
                    <li>2.2.5. Передача информации в ёЖ-3 ИА, ОДУ, РДУ о получении ПГ СК-11 в ДЦ.</li>
                    <li>2.2.6. Передача информации в ёЖ-3 ИА о получении ПГ 2СЗ (ДДГ) и информации о его акцепте в ОДУ Востока.</li>
                    <li>2.2.7. Передача информации в ёЖ-3 ИА, ОДУ, РДУ об акцепте ПГ СК-11.</li>
                    <li>2.2.8. Передача в Централизованную часть Системы ИА квитанций об успешном/неуспешном получении и записи ПГ в ДЦ.</li>
                  </ul>
                </li>
              </ul>
            </li>
            <li>3. В Централизованной части 2СЗ (На уровне ОДУ Востока):
            <ul type="disc">
              <li>3.1. Обработка НСИ:
                <ul type="disc">
                  <li>3.1.1. Получении НСИ из Централизованной части Системы на уровне ИА.</li>
                </ul>
              </li>
              <li>3.2. Обработка ПГ:
                <ul type="disc">
                  <li>3.2.1. Загрузка (запрос, инициированный Системой, и синхронный ответ ПАК «ОпАМ») ПГ из ПАК «ОпАМ» ОДУ Востока. </li>
                  <li>3.2.2. Загрузка ПГ из Мегаточки.</li>
                  <li>3.2.3. Преобразование ПГ в ПГ СК-11.</li>
                  <li>3.2.4. Распространение ПГ СК-11 и результатов обработки данных (п.3.3) в ДЦ ОЗ ОДУ Востока и ИА.</li>
                  <li>3.2.5. Получение и обработка квитанций об успешной/неуспешной об-работке ПГ в ДЦ [2СЗ].</li>
                  <li>3.2.6. Акцепт ПГ СК-11 2СЗ;</li>
                  <li>3.2.7. Контроль распространения ПГ СК-11 и результатов обработки данных (п.3.3) в ДЦ ОЗ ОДУ Востока и ИА.</li>
                  <li>3.2.8. Контроль передачи ПГ СК-11 из ДЦ ОЗ ОДУ Востока во внешние системы: ОИК СК-11, ПАК «MODES-Terminal».</li>
                </ul>
              </li>
              <li>3.3. Обработка данных:
                <ul type="disc">
                  <li>3.3.1. Расчет агрегированных параметров «сумматоры» по объектам 2 синхронной зоны.</li>
                  <li>3.3.2. Расчет агрегированных параметров «ГОУ» по объектам 2 син-хронной зоны.</li>
                  <li>3.3.4. Уведомления о событиях</li>
                </ul>
              </li>
              <li>3.4. Передача данных:
                <ul type="disc">
                  <li>3.4.1. Передача признака акцепта ПГ в ПАК «ОпАМ» ОДУ Востока и ДЦ ОЗ ОДУ Востока.</li>
                  <li>3.4.2.  Передача в Распределенную часть уровня ИА ПГ 1СЗ и 2СЗ и ин-формации о его акцепте.</li>
                </ul>
              </li>
            </ul>
            </li>
            <li>4. В Распределенных частях Системы 2СЗ (На уровне ОДУ Востока, РДУ ОЗ ОДУ Востока):
             <ul type="disc">
              <li>4.1. Получение данных:
                  <ul type="disc">
                    <li>4.1.1. Получение НСИ из Централизованной части Системы на уровне ИА. </li>
                    <li>4.1.2. Получение ПГ СК-11 и результатов обработки данных (п.3.3) из Централизованной части Системы ОДУ Востока.</li>
                  </ul>
                </li>
                <li>4.2. Передача данных:
                  <ul type="disc">
                    <li>4.2.1. Настройка состава параметров, передаваемых в ОИК СК-11 ДЦ.</li>
                    <li>4.2.2. Передача ПГ СК-11 в ОИК СК-11 ДЦ.</li>
                    <li>4.2.3. Передача квитанций в Централизованную часть Системы на уровне ОДУ Востока об успешной/неуспешной обработке ПГ в ДЦ [2СЗ].</li>
                    <li>4.2.4. Передача акцептованного ПГ СК-11 в ПАК «MODES-Terminal».</li>
                    <li>4.2.5. Передача акцептованного ПГ СК-11 в ПАК «СРДК».</li>
                    <li>4.2.6. Передача информации в ёЖ-3 ДЦ о акцепте ПГ СК-11;</li>
                    <li>4.2.7. Передача информации в ёЖ-3 ДЦ о получении ПГ СК-11 в ДЦ.</li>
                  </ul>
                </li>
             </ul>
            </li>
          </ul>

          <Page className="block1">
            <TitlePage name={`user_interface`}>3.ИНТЕРФЕЙС ПОЛЬЗОВАТЕЛЯ</TitlePage>
            <DescriptionPage name={`user_authorization`}>3.1. Авторизация пользователей</DescriptionPage>
            <p>При первом переходе в систему пользователь попадает на форму авторизации (Рисунок 1). </p>
            <BigPicture>
              <img src={image186} alt="image186" />
              <AltPicture>Рисунок 1 – Форма авторизации пользователей</AltPicture>
            </BigPicture>
            <p>Пользователю необходимо авторизоваться в системе при помощи Логина и Пароля от доменной учетной записи (AD).  После корректного ввода данных авторизации необходимо нажать на кнопку<b> «Войти»</b>.</p>
            <p>Если при попытке авторизации выводится уведомление об ошибке, тогда необходимо проверить введенные учетные данные и повторить попытку входа в Систему. Если учетные данные верны, тогда необходимо обратиться к Администратору системы для проверки доступа учетной записи.</p>
            <p>При успешной авторизации пользователь попадает на форму согласно своей роли в Системе по умолчанию (Администратор Системы – форма «Настройки», Администратор НСИ, Администратор НСИ ЕСС, Наблюдатель – форма «НСИ», Технолог – форма «Плановые графики»), Рисунок 2.
              <BigPicture>
                <img src={image187} alt="image187" />
                <AltPicture>Рисунок 2 – Стартовое окно системы для пользователей</AltPicture>
              </BigPicture>
            </p>
            <p>Для переключения между Централизованной и Распределенной частями Системы в экземплярах СПРГ ИА, ОДУ Востока отображается переключатель на главной панели (Рисунок 3) <BigPicture>
              <img src={image188} alt="image188" />
              <AltPicture>Рисунок 3 – Кнопка переключения между Централизованной и Распределенной частями Системы</AltPicture>
            </BigPicture></p>
            <p>Для пользователя, в зависимости от роли, доступны разделы:</p>


            <ul>
              <li>1) <b>НСИ.</b> В Централизованной части Системы раздел предназначен для загрузки, просмотра, сравнения загруженных версий НСИ, распространения и контроля распространения НСИ в Распределенные части Системы, а также для сопоставления объектов ГОУ с объектами ОИК СК-11 и распространения связей в Распределенные части Системы.</li>
              <p>В Распределенной части Системы раздел «НСИ» предназначен для просмотра НСИ, просмотра истории загрузки НСИ, сравнения загруженных версий НСИ, сопоставления реестров СРПГ и Сумматоры с объектами ОИК СК-11.</p>
              <li>2) <b>Плановые графики.</b> В Централизованной части Системы раздел предназначен для загрузки и распространения ПГ в Распределенные части Системы, акцепта ПГ, контроля распространения и акцепта ПГ, просмотра распространенных ПГ.  </li>
              <p>В Распределенной части Системы раздел предназначен для просмотра загруженных ПГ, распространения и контроля распространения ПГ во внешние системы, резервной загрузки ПГ в случае аварийных ситуаций.</p>
              <li>3) <b>Администрирование.</b> Данный раздел Системы предназначен для добавления групп пользователей, зарегистрированных в AD в Систему, связи добавляемых групп пользователей с предопределенными ролями.</li>
              <li>4) <b>Настройки.</b> Данный раздел Системы предназначен для настройки взаимодействия с внешними системами, для настройки принципов работы СРПГ. Данный раздел присутствует в распределенной части (во всех ДЦ) и централизованной части (ИА, ОДУ Востока) Системы. Описание по работе с настройками СРПГ представлено в Руководстве администратора.</li>
              <li>5) <b>Журналирование.</b> Данный раздел Системы предназначен для журналирования действий пользователей и взаимодействия с внешними системами в Системе СРПГ СК-11.</li>
              <li>6) <b>Уведомления.</b> Раздел предназначен для настройки почтового уведомления (подписки) текущего пользователя на события распространения НСИ в ИУС «СРПГ».</li>
            </ul>
          </Page>

          <Page className="block2">
            <DescriptionPage name={`working_with_partitions_in_a_centralized`}>3.2. Работа с разделами в Централизованной части Системы (ИА, ОДУ Востока)</DescriptionPage>
            <DescriptionPage name={`nsi`}>3.2.1. Работа с НСИ</DescriptionPage>
            <p>В Централизованной части Системы раздел предназначен для загрузки, просмотра, сравнения загруженных версий НСИ, распространения и контроля распространения НСИ в Распределенные части Системы, а также для сопоставления объектов ГОУ с объектами ОИК СК-11 и распространения связей в Распределенные части Системы. </p>
            <p>Загрузка и распространение НСИ доступна только пользователю с ролью Администратор НСИ ЕСС (ИА).</p>
            <p>Просмотр данных НСИ доступен для пользователей с ролью Администратор НСИ, Администратор НСИ ЕСС, Наблюдатель, Технолог.</p>
            <p>Для перехода к функциональности по работе с НСИ необходимо перейти в раздел     на главной рабочей панели.</p>
            <p>Откроется раздел «НСИ» <img src={image189} alt="image189" /> (Рисунок 4).
              <BigPicture>
                <img src={image190} alt="image190" />
                <AltPicture>Рисунок 4 – Интерфейс раздела работы с НСИ</AltPicture>
              </BigPicture>
            </p>
            <h5>Загрузка НСИ</h5>
            <p>Для выполнения загрузки НСИ необходимо нажать на рабочей панели кнопку <img src={image191} alt="image191" />  , затем в выпадающем списке выбрать вариант загрузки НСИ из ПАК ЕСС или из файла.</p>
            <p>Откроется модальное окно, в котором необходимо выбрать дату в календаре, с которой изменения должны начать действовать и нажать кнопку <img src={image192} alt="image192" /> (Рисунок 5).
              По умолчанию, в календаре выбрана следующая за текущей дата. Можно выбрать только дату больше текущей.</p>
            <p>Так же в верхней части окна отображается информация о последней сохраненной версии НСИ <img src={image193} alt="image193" /> . Указывается дата НСИ, с которой изменения начинают действовать, а также дата и время загрузки НСИ.
              <BigPicture>
                <img src={image194} alt="image194" />
                <AltPicture>Рисунок 5 – Окно загрузки НСИ за выбранную дату</AltPicture>
              </BigPicture></p>
            <p>При загрузке данных НСИ выполняются проверки:</p>
            <ul>
              <li>наличие дублей в реестрах;</li>
              <li>наличие связи ГОУ с РГЕ, которое отсутствует в Реестре СРПГ;</li>
              <li>наличие связи ГОУ с сечением, которое отсутствует в Реестре СРПГ;</li>
              <li>наличие связи ГОУ с сечением и РГЕ одновременно;</li>
              <li>наличие в Карте ведения ссылок на несуществующее объекты;</li>
              <li>наличие в Карте ведения ссылок на несуществующие родительские объекты.</li>
            </ul>
            <p>При наличии одного из ограничений, ошибки фиксируются в протокол ошибок (Рисунок 7).</p>
            <p>После успешной загрузки НСИ, на форме отображается статус загрузки, время загрузки НСИ, начало действия НСИ, инициатор загрузки НСИ. (Рисунок 6).
              <BigPicture>
                <img src={image195} alt="image195" />
                <AltPicture>Рисунок 6 – Форма настроек загрузки НСИ</AltPicture>
              </BigPicture>
            </p>
            <p>Для просмотра ошибок по результатам загрузки НСИ, необходимо нажать на кнопку <img src={image196} alt="image196" />.</p>
            <p>После нажатия откроется форма протокола ошибок (Рисунок 7).
              <BigPicture>
                <img src={image197} alt="image197" />
                <AltPicture>Рисунок 7 – Форма протокол ошибок</AltPicture>
              </BigPicture></p>
            <p>Для выгрузки протокола ошибок необходимо нажать на кнопку <img src={image198} alt="image198" />.</p>
            <p>Для просмотра результатов сравнения загруженной версии с последней сохраненной в Системе версией НСИ, необходимо нажать на кнопку<img src={image199} alt="image199" /> на форме загрузки НСИ.</p>
            <p>После нажатия откроется форма протокола изменений НСИ (Рисунок 8).
              <BigPicture>
                <img src={image200} alt="image200" />
                <AltPicture>Рисунок 8 – Форма протокола сравнения версий НСИ </AltPicture>
              </BigPicture>
            </p>
            <p>Для выгрузки результатов изменений НСИ необходимо нажать на кнопку <img src={image198} alt="image198" />.</p>
            <p>Для отмены загрузки версии НСИ необходимо нажать на кнопку  <img src={image201} alt="image201" />  на форме загрузки НСИ. Загруженная версия НСИ будет отменена и недоступна для распространения в ДЦ.</p>
            <p>Для сохранения и распространения загруженной версии НСИ необходимо нажать на кнопку <img src={image202} alt="image202" />   на форме загрузки НСИ.</p>
            <p>Откроется форма выбора ДЦ для распространения загруженной версии НСИ. (Рисунок 9).
              <BigPicture>
                <img src={image200} alt="image200" />
                <AltPicture>Рисунок 9 – Форма выбора ДЦ для распространения загруженной НСИ </AltPicture>
              </BigPicture>
            </p>
            <p>На форме доступны следующие инструменты:</p>
            <TextPage>
              <br />
              <IconContainer>
                <div>
                  <Icon width={16} name="search" />
                </div>
                - Выполнить фильтр по полю. <br />
              </IconContainer>
            </TextPage>
            <p><img src={image204} alt="image204" /> - Выбрать все объекты/Сбросить выделение всех выбранных объектов.</p>
            <p>Для распространения НСИ в выбранные в списке ДЦ необходимо нажать <img src={image205} alt="image205" /> , для отмены выбора нажать кнопку <img src={image206} alt="image206" />   .</p>
            <p>По результату распространения НСИ в выбранные ДЦ, выводится уведомление со статусом передачи НСИ в выбранные ДЦ. (Рисунок 10).
              <BigPicture>
                <img src={image207} alt="image207" />
                <AltPicture>10 – Уведомление об общем результате распространения НСИ в выбранные ДЦ </AltPicture>
              </BigPicture>
            </p>
            <h5>Просмотр реестров НСИ</h5>
            <p>Для просмотра сохраненной НСИ за выбранную дату, необходимо открыть календарь (Рисунок 11), затем выбрать год, месяц, день и нажать кнопку <img src={image208} alt="image208" /> .</p>
            <p>Зеленым цветом выделены даты, на которые была выполнена загрузка НСИ.
              <BigPicture>
                <img src={image209} alt="image209" />
                <AltPicture>Рисунок 11 – Форма выбора даты для просмотра НСИ </AltPicture>
              </BigPicture></p>
            <p>После выбора даты, будут загружены актуальные на выбранную дату реестры НСИ (Рисунок 12).
              <BigPicture>
                <img src={image210} alt="image210" />
                <AltPicture>Рисунок 12 – Форма просмотра реестров НСИ</AltPicture>
              </BigPicture></p>
            <p>В левой области формы отображается реестр ДЦ, в правой области формы отображаются реестры ГОУ, СРПГ, Сумматоры. По умолчанию выбран реестр ГОУ.</p>
            <p>При выборе ДЦ, объекты в правой части формы отображаются согласно картам ведения. Если ни один ДЦ не выбран, то в правой части формы отображаются все загруженные объекты НСИ.</p>
            <p>Для просмотра реестров СРПГ необходимо нажать на вкладку «СРПГ» в правой области формы <img src={image211} alt="image211" />, затем выбрать из списка необходимый тип объектов реестра СРПГ (Рисунок 13).
              <BigPicture>
                <img src={image210} alt="image210" />
                <AltPicture>Рисунок 13 – Список типов объектов реестра СРПГ</AltPicture>
              </BigPicture></p>
            <p>Для просмотра реестров Сумматоры, необходимо нажать на вкладку «Сумматоры».</p>
            <TextPage>
              На форме доступны следующие инструменты:
              <br />
              <IconContainer>
                <div>
                  <Icon width={16} name="search" />
                </div>
                - Выполнить фильтр по полю. <br />
              </IconContainer>
              <img src={image74} alt="image31" /> - Выполнить сортировку по полю . <br />
              <br />
            </TextPage>
            <p>Для просмотра карт ведения по объектам, необходимо напротив объекта нажать на кнопку <img src={image213} alt="image213" /> . Откроется окно с просмотром заинтересованных в объекте ДЦ.
              <BigPicture>
                <img src={image214} alt="image214" />
              </BigPicture></p>
            <h5>Сравнение НСИ за 2 выбранные даты</h5>
            <p>Для просмотра результатов сравнения НСИ за даты, необходимо нажать на кнопку <img src={image215} alt="image215" /> .</p>
            <p>Откроется форма просмотра сравнения НСИ за 2 выбранные даты. (Рисунок 14).
              <BigPicture>
                <img src={image216} alt="image216" />
                <AltPicture>Рисунок 14 – Форма сравнения НСИ за 2 выбранные даты</AltPicture>
              </BigPicture></p>
            <p>На форме необходимо выбрать 2 даты в полях <img src={image217} alt="image217" /> за которые необходимо выполнить сравнение версий НСИ, затем нажать кнопку сравнить <img src={image218} alt="image218" /> .</p>
            <p>На форме выводится информация по результатам сравнения НСИ за 2 выбранные даты (Рисунок 15).
              <BigPicture>
                <img src={image219} alt="image219" />
                <AltPicture>Рисунок 15 – Результаты сравнения НСИ за 2 выбранные даты</AltPicture>
              </BigPicture>
            </p>
            <p>Для выгрузки результатов сравнений НСИ необходимо нажать на кнопку <img src={image220} alt="image220" />. </p>
            <h5>Распространение НСИ</h5>
            <p>Для перехода к форме контроля распространения НСИ необходимо перейти на вкладку <img src={image221} alt="image221" /> .</p>
            <p>Откроется форма с деревом ДЦ и статусами распространения последней загруженной версии НСИ (Рисунок 16).
              <BigPicture>
                <img src={image222} alt="image222" />
                <AltPicture>Рисунок 16 – Форма отображение контроля распространения НСИ в ДЦ</AltPicture>
              </BigPicture>
            </p>
            <TextPage>
              На форме доступны следующие инструменты:
              <br />
              <IconContainer>
                <div>
                  <Icon width={16} name="search" />
                </div>
                - Выполнить фильтр по полю. <br />
              </IconContainer>
              <img src={image74} alt="image31" /> - Выполнить сортировку по полю . <br />
              <br />
            </TextPage>
            <p>Для повтора распространения НСИ во все ДЦ со статусом «Ошибка» необходимо  нажать на кнопку <img src={image223} alt="image223" /> .</p>
            <h5>Сопоставление объектов НСИ с объектами ОИК СК-11 в ИА</h5>
            <p>Централизованная часть СРПГ СК-11 ИА.</p>
            <p>Возможность сопоставления объектов, распространения в ДЦ и выполнение настроек характеристик по объектам доступна только пользователю с ролью Администратор НСИ.</p>
            <p>Для сопоставления объектов ГОУ реестра НСИ ПАК ЕСС с объектами ОИК СК-11 в Централизованной части Системы, необходимо перейти на вкладку  <img src={image227} alt="image227" /> .</p>
            <p>Откроется форма редактора сопоставления объектов НСИ ПАК ЕСС и объектов ОИК СК-11. (Рисунок 17).
              <BigPicture>
                <img src={image224} alt="image224" />
                <AltPicture>Рисунок 17 – Форма редактора сопоставления объектов типа ГОУ с ОИК СК-11</AltPicture>
              </BigPicture></p>
            <p>Для получения списка объектов из ОИК СК-11 необходимо нажать на кнопку <img src={image225} alt="image225" /> в области объектов ОИК СК-11. Загруженные объекты временно хранятся в СРПГ в соответствии с настройками, заданными Администратором Системы. </p>
            <p>Загрузка объектов из ОИК СК-11 может иметь несколько состояний:
              <BigPicture>
                <img src={image228} alt="image228" />
              </BigPicture>
              <BigPicture>
                <img src={image229} alt="image229" />
              </BigPicture>
              <BigPicture>
                <img src={image230} alt="image230" />
              </BigPicture>
            </p>
            <p>После успешной загрузки объектов из ОИК СК-11, загруженные объекты отображаются в интерфейсе. </p>
            <p>Для выполнения сопоставления объектов необходимо найти в списке объектов ПАК ЕСС объект, далее нажать на объект, чтобы загорелся индикатор <img src={image231} alt="image231" /> , в списке объектов ОИК СК-11 необходимо также найти объект, с которым необходимо выполнить сопоставление и нажать на кнопку <img src={image232} alt="image232" /> (если объект еще не имеет связей) или <img src={image233} alt="image233" /> (если объект уже имеет связи). При успешном выполнении сопоставления в области объекты ПАК ЕСС у сопоставляемого объекта установится UID. </p>
            <p>Для удаления связи объектов в области «Объекты ПАК ЕСС» необходимо напротив объекта нажать на кнопку <img src={image234} alt="image234" />. </p>
            <p>Для сохранения изменений на форме необходимо нажать на кнопку <img src={image235} alt="image235" /> . </p>
            <p>Для отмены изменений на форме необходимо нажать на кнопку <img src={image236} alt="image236" /></p>
            <p>Для фильтрации по сопоставленным и не сопоставленным объектам необходимо в фильтре установить значение <img src={image237} alt="image237" />.</p>
            <p>Для выгрузки результатов сопоставления необходимо нажать на <img src={image238} alt="image238" /> и выбрать из списка интересующий состав объектов.
              <img src={image239} alt="image239" /></p>
            <p> Для проверки наличия связи одного объекта ОИК СК-11 с несколькими объектами СРПГ необходимо нажать на кнопку <img src={image240} alt="image240" />. Откроется окно со списком всех дублирующих связей с UID ОИК СК-11 (Рисунок 18). </p>
            <p>В окне «Множественные связи» пункта меню «НСИ» -&gt; «Сопоставление объектов с СК-11» реализована возможность проверки выполненного сопоставления объектов СРПГ с объектами ОИК СК-11. Для запуска проверки необходимо нажать на кнопку <img src={image241} alt="image241" /> , в результате выполнения которой отобразятся сопоставленные (действующие и недействующие) объекты, имеющие ошибки. В случае отсутствия ошибок в табличной части окна «Множественные связи» отобразится уведомление «Нет данных».</p>
            <p>Автоматически выполняемые проверки в СРПГ (недопустимые множественные связи):</p>
            <p>− множественная связь нескольких объектов СРПГ одного типа с одним объектом ОИК СК-11;</p>
            <p>− множественная связь разных типов объектов СРПГ, имеющих одинаковый UID характеристики (UID типа измерения ОИК СК-11 - MeasurementType), с одним объектом ОИК СК-11.</p>
            <p> По завершению выполнения проверки, объекты с некорректными связями будут выведены на экран с оранжевой заливкой фона. </p>
            <p> Для сброса результата проверки необходимо открыть заново окно «Множественные связи».
              <BigPicture>
                <img src={image242} alt="image242" />
                <AltPicture>Рисунок 18 – Форма множественные связи сопоставления объектов типа ГОУ с ОИК СК-11</AltPicture>
              </BigPicture> </p>
            <p>Если ранее сопоставленного объекта нет в выбранной версии НСИ, то такие объекты отображаются в виде даты вместо наименования. Дата красным цветом отображается для объектов, которые ранее действовали в НСИ, дата зеленым цветом отображается для объектов, которые еще не действуют. </p>
            <p>На форме «Множественные связи» есть возможность сбросить связи, для этого необходимо нажать на кнопку <img src={image234} alt="image234" />  в поле «Действия» напротив объекта. </p><p>Для выгрузки результата проверки, необходимо нажать на кнопку <img src={image198} alt="image198" />  .</p>
            <p>Для сохранения изменений на форме необходимо нажать на кнопку <img src={image235} alt="image235" />  . </p>
            <p>Для отмены изменений на форме необходимо нажать на кнопку <img src={image236} alt="image236" /></p>
            <p> Для просмотра связей по объекту в области объектов ОИК СК-11 у объектов с зеленым индикатором   необходимо нажать на кнопку <img src={image213} alt="image213" />  (Рисунок 19).</p>
            <BigPicture>
              <img src={image243} alt="image243" />
              <AltPicture>Рисунок 19 – Просмотр связей</AltPicture>
            </BigPicture>
            <p> После нажатия на «Просмотр связей» откроется окно «Просмотр связи по объекту» с отображением перечня объектов сопоставленных с выбранным UID ОИК СК-11 (Рисунок 20).
              <BigPicture>
                <img src={image243} alt="image243" />
                <AltPicture>Рисунок 20 – Форма просмотра связей объекта типа ГОУ с ОИК СК-11</AltPicture>
              </BigPicture> </p>
            <p>В окне «Просмотр связи по объекту» есть возможность сбросить связь, для этого необходимо нажать на кнопку <img src={image234} alt="image234" />   в поле «Действия» напротив объекта. </p>
            <p>Для сохранения изменений в окне «Просмотр связи по объекту»  необходимо нажать на кнопку <img src={image235} alt="image235" />  . </p>
            <p>Для отмены изменений в окне «Просмотр связи по объекту» необходимо нажать на кнопку <img src={image236} alt="image236" /> </p>
            <h5>Распространение связей ГОУ в ДЦ</h5>
            <p>Для передачи списка сопоставленных ГОУ в СРПГ ДЦ, необходимо нажать на форме «Сопоставление объектов с СК-11» на кнопку <img src={image245} alt="image245" /> .</p>
            <p>По результатам успешного распространения ГОУ во все ДЦ станет активна кнопка <img src={image246} alt="image246" />. </p>
            <p>В случае, если ГОУ распространены не во все ДЦ станет активна кнопка <img src={image247} alt="image247" /> .</p>
            <p>При нажатии на кнопку протокола, откроется форма для просмотра результатов распространения. (Рисунок 21)
              <BigPicture>
                <img src={image248} alt="image248" />
                <AltPicture>Рисунок 21 – Форма протокола с результатами распространения результатов сопоставления ГОУ</AltPicture>
              </BigPicture>
            </p>
            <p>В случае, если распространение не выполнилось ни в один ДЦ, станет активна кнопка <img style={{width: 90,height:30}} src={image249} alt="image249" />  .</p>
          </Page>

          <Page className="block3">
            <DescriptionPage name={`scheduled_schedules`}>3.2.2. Работа с Плановыми графиками</DescriptionPage>
            <p>Раздел «Плановые графики» предназначен для загрузки ПГ, просмотра информации по ПГ, выгрузке ПГ в xml, контролю распространения ПГ в ДЦ, акцепту ПГ.</p>
            <p>Раздел доступен для пользователей с ролью Администратор Системы, Администратор НСИ, Администратор НСИ ЕСС, Наблюдатель, Технолог.</p>
            <p>Для перехода к функциональности работы с плановыми графиками необходимо нажать раздел <img src={image250} alt="image250" />  на главной рабочей панели.</p>
            <h5>Общее описание загрузки и распространения ПГ в СРПГ СК-11</h5>
            <NumericOl>
              <NumericLi>При загрузке ПГ из ОПАМ/Мегаточки/файла выполняется разбор полученного ПГ в СРПГ.
                <NumericOl>
                  <NumericLi>Согласно дате начала действия ПГ определяется актуальная версия НСИ, согласно настройкам в конфигурации определяется СЗ экземпляра СРПГ СК-11. Актуальная версия НСИ фильтруется по СЗ и полученные объекты сравниваются с объектами в ПГ. Объекты, отсутствующие в отфильтрованной версии НСИ исключаются из ПГ.</NumericLi>
                  <NumericLi>Согласно настройке «Характеристики» определяются для каждого типа ПГ характеристики, которые необходимо включить в ПГ. Если в ПГ присутствуют характеристики, не удовлетворяющие настройке, то такие характеристики исключаются из исходного ПГ. Преобразованный ПГ записывается в СРПГ СК-11.</NumericLi>
                </NumericOl>
              </NumericLi>
              <NumericLi>Выполняется расчет значений ГОУ, согласно иерархической структуре объектов ГОУ из НСИ. Рассчитанные значения записываются в ПГ СРПГ СК-11.</NumericLi>
              <NumericLi>Выполняется проверка на необходимость расчета сумматоров, согласно настройке «Загрузка ПГ» для каждого типа ПГ. При наличии включенной опции, выполняется расчет значений сумматоров, согласно иерархической структуре объектов сумматоров из НСИ. Рассчитанные значения записываются в ПГ СРПГ СК-11.</NumericLi>
              <NumericLi>Выгрузка ПГ формата XML содержит состав ПГ, подготовленный по результатам действий пунктов 1,2 и 3.</NumericLi>
              <NumericLi>Распространение ПГ в ДЦ выполняется автоматически по факту завершения процесса загрузки ПГ в СРПГ. Распространение ПГ выполняется во все ДЦ параллельно.</NumericLi>
              <NumericLi>Формирование ПГ для отправки в ДЦ. Из БД считывается загруженный и рассчитанный ПГ, далее согласно картам ведения для каждого ДЦ определяется состав ПГ и формируются отдельные пакеты, которые не хранятся в БД ИА, а сразу распространяются в ДЦ.</NumericLi>
            </NumericOl>
            {/*<p>1.При загрузке ПГ из ОПАМ/Мегаточки/файла выполняется разбор полученного ПГ в СРПГ.</p>*/}
            {/*<p>     1.1. Согласно дате начала действия ПГ определяется актуальная версия НСИ, согласно настройкам в конфигурации определяется СЗ экземпляра СРПГ СК-11. Актуальная версия НСИ фильтруется по СЗ и полученные объекты сравниваются с объектами в ПГ. Объекты, отсутствующие в отфильтрованной версии НСИ исключаются из ПГ.</p>*/}
            {/*<p>     1.2. Согласно настройке «Характеристики» определяются для каждого типа ПГ характеристики, которые необходимо включить в ПГ. Если в ПГ присутствуют характеристики, не удовлетворяющие настройке, то такие характеристики исключаются из исходного ПГ. Преобразованный ПГ записывается в СРПГ СК-11.</p>*/}
            {/*<p>   2. Выполняется расчет значений ГОУ, согласно иерархической структуре объектов ГОУ из НСИ. Рассчитанные значения записываются в ПГ СРПГ СК-11.</p>*/}
            {/*<p>   3. Выполняется проверка на необходимость расчета сумматоров, согласно настройке «Загрузка ПГ» для каждого типа ПГ. При наличии включенной опции, выполняется расчет значений сумматоров, согласно иерархической структуре объектов сумматоров из НСИ. Рассчитанные значения записываются в ПГ СРПГ СК-11.</p>*/}
            {/*<p>   4. Выгрузка ПГ формата XML содержит состав ПГ, подготовленный по результатам действий пунктов 1,2 и 3.</p>*/}
            {/*<p>   5. Распространение ПГ в ДЦ выполняется автоматически по факту завершения процесса загрузки ПГ в СРПГ. Распространение ПГ выполняется во все ДЦ параллельно. </p>*/}
            {/*<p>  6. Формирование ПГ для отправки в ДЦ. Из БД считывается загруженный и рассчитанный ПГ, далее согласно картам ведения для каждого ДЦ определяется состав ПГ и формируются отдельные пакеты, которые не хранятся в БД ИА, а сразу распространяются в ДЦ. </p>*/}
            <h5>Загрузка и просмотр ПГ</h5>
            <p>Загрузка ПГ доступна пользователю с ролью Технолог.</p>
            <p>Для загрузки ПГ необходимо выбрать тип ПГ, Номер ПБР [1СЗ]/ПБР [2СЗ] (при необходимости), дату (при необходимости) и нажать кнопку <img src={image192} alt="image192" /> . При успешной загрузке ПГ, ПГ отобразится в списке загруженных ПГ.</p>
            <p>Распространение ПГ в ДЦ выполняется автоматически по факту завершения процесса загрузки ПГ в СРПГ СК-11. Распространение ПГ выполняется во все ДЦ одновременно. </p>
            <p>Необходимый состав ПГ для каждого ДЦ определяется согласно картам ведения, формируются отдельные пакеты, которые не хранятся в СРПГ СК-11 ИА, а сразу распространяются в ДЦ. </p>
            <p>Для просмотра детальной информации по ПГ необходимо выделить ПГ, внизу формы откроется детальная информация.</p>
            <p>Для выгрузки ПГ в файл формата xml, необходимо в детализации ПГ выбрать действие <img src={image251} alt="image251" /> и выбрать вид выгрузки обычный или детальный ПГ. В детальном ПГ выполняется выгрузка с учетом наименований объектов для быстрого поиска необходимой информации для пользователей Системы.
              <BigPicture>
                <img src={image252} alt="image252" />
                <AltPicture>Рисунок 22 – Форма загрузки и просмотра ПГ</AltPicture>
              </BigPicture></p>
            <TextPage>
              На форме также доступны следующие инструменты:
              <br />
              <IconContainer>
                <div>
                  <Icon width={16} name="search" />
                </div>
                - Выполнить фильтр по полю. <br />
              </IconContainer>
              <img src={image74} alt="image31" /> - Выполнить сортировку по полю . <br />
              <br />
            </TextPage>
            <p>В табличной части формы «Список плановых графиков» централизованной части СРПГ отображаются следующие параметры:</p>
            <p>Опция выбора ПГ <img src={image253} alt="image253" />  – при включении опции (<img src={image254} alt="image254" /> ) внизу табличной части отображается более детальная информация о ПГ:</p>
            <ul>
              <li>Название – отображает тип и дату ПГ в формате «&lt;Тип ПГ&gt; на &lt;ДД.ММ.ГГГГ&gt;»;</li>
              <li>Тип пакета – отображается тип отправляемого пакета ПГ (одно из следующих значений: ППБР, ПБР, ПЭР, ПДГ, УДДГ, ДДГ);</li>
              <li>Временной интервал (начало) – отображает время начала действия, выбранного ПГ в формате «ДД.ММ.ГГГГ чч:мм:сс»;</li>
              <li>Временной интервал (конец) – отображает время окончания действия, выбранного ПГ в формате «ДД.ММ.ГГГГ чч:мм:сс»;</li>
              <li>Действие – позволяет выбрать тип (Детальный XML, Обычный XML) файла XML с ПГ, который необходимо экспортировать;  </li>
              <li>Акцепт в ОпАМ – отображает дату и время команды акцепта, направленной в ПАК «ОпАМ» из СРПГ ИА (ОДУ Востока) в формате «ДД.ММ.ГГГГ чч:мм:сс». </li>
              <li>Название – отображает тип и дату ПГ в формате «&lt;Тип ПГ &gt; на &lt;ДД.ММ.ГГГГ&gt;»;</li>
              <li>­Время формирования – указывается время формирования ПГ в СРПГ в формате «ДД.ММ.ГГГГ чч:мм:сс»;</li>
              <li>Тип – указывается тип ПГ в формате «ПБР-&lt;номер&gt;», «УДДГ-&lt;номер&gt;», «ППБР», «ПБР», «ПЭР», «ПДГ», «ДДГ»;</li>
              <li>­СЗ – указывается синхронная зона ПГ: значение «1» или «2»;</li>
              <li>­Инициатор загрузки ПГ – указывается пользователь (в формате фамилия и инициалы), который инициировал загрузку ПГ;</li>
              <li>­Статус ПГ – указывается статус распространения/записи ПГ;</li>
              <li>­Время акцепта – указывается дата и время команды акцепта ПГ в формате «ДД.ММ.ГГГГ чч:мм:сс»;</li>
              <li>­Инициатор команды акцепта – в поле фиксируется пользователь (в формате фамилия и инициалы) или автоматический способ акцепта, в случае настройки автоматической команды акцепта для данного типа ПГ.</li>
            </ul>
            <h5>Просмотр статуса распространения загруженных ПГ</h5>
            <p>Для просмотра детальной информации по ПГ, необходимо нажать на кнопку <img src={image255} alt="image255" />  у необходимого ПГ.</p>
            <p>Откроется форма со статусами распространения выбранного ПГ в ОИК СК-11 ДЦ, ПАК «MODES-Terminal» ДЦ, ИУС СРДК ДЦ (Рисунок 23).
              <BigPicture>
                <img src={image252} alt="image252" />
                <AltPicture>Рисунок 23 – Форма контроля распространения ПГ в ДЦ</AltPicture>
              </BigPicture>
            </p>
            <p> При необходимости пользователь может изменить фильтры просмотра распространения ПГ и выбрать другую дату, а также ПГ.</p>
            <p> Если в настройках для типа ПГ установлен способ акцепта «ручной», то на форме будет активна кнопка <img src={image257} alt="image257" /> . При нажатии на кнопку выполняется акцепт ПГ и распространение команды акцепта в ДЦ, от которых получена квитанция об успешной записи неакцептованного ПГ в ОИК СК-11. Если из каких-то ДЦ не получена квитанция об успешной записи неакцептованного ПГ в ОИК СК-11, то команда акцепта будет автоматически отправляться по факту поступления квитанций от ДЦ в ИА.</p>
            <p> При возникновении ошибки распространения ПГ в ДЦ необходимо выбрать ДЦ, выставив опцию напротив ДЦ  <img src={image258} alt="image258" /> и нажать на кнопку <img src={image223} alt="image223" />   . При повторной отправке ПГ в ДЦ повторно формируются пакеты с ПГ, только в разрезе тех ДЦ, которые выбрал пользователь и далее ПГ распространяются в ДЦ.</p>
            <p> При возникновении ошибки распространения команды акцепта в ДЦ необходимо выбрать ДЦ, выставив опцию напротив ДЦ <img src={image258} alt="image258" />  и нажать на кнопку <img src={image259} alt="image259" /> .</p>
            <TextPage>
              На форме доступны следующие инструменты:
              <br />
              <IconContainer>
                <div>
                  <Icon width={16} name="search" />
                </div>
                - Выполнить фильтр по полю. <br />
              </IconContainer>
              <img src={image74} alt="image31" /> - Выполнить сортировку по полю . <br />
              <br />
            </TextPage>
            <p> <img src={image204} alt="image204" /> - Выбрать все объекты/Сбросить выделение всех выбранных объектов.</p>
          </Page>

          <DescriptionPage name={`working_with_partitions_in_distributed`}>3.3. Работа с разделами в Распределенных части Системы (ОДУ, РДУ)</DescriptionPage>
          <DescriptionPage name={`work_nsi`}>3.3.2. Работа с НСИ</DescriptionPage>
          <p>В Распределенных частях раздел предназначен для просмотра НСИ, сравнения загруженных версий НСИ, сопоставления реестров НСИ ДЦ с объектами ОИК СК-11.</p>
          <p>Просмотр данных НСИ доступен для пользователей с ролью Администратор НСИ, Администратор НСИ ЕСС, Наблюдатель, Технолог.</p>
          <p>Для перехода к функциональности работы с НСИ необходимо нажать раздел <img src={image189} alt="image189" />   на главной рабочей панели.</p>
          <p>Просмотр реестров НСИ и Сравнение НСИ за 2 выбранные даты описаны в разделе 3.2.1 данной инструкции.</p>
          <h5>Просмотр истории загрузки НСИ</h5>
          <p>Для просмотра истории полученных версий НСИ в ДЦ необходимо нажать на кнопку <img src={image260} alt="image260" /> .</p>
          <p>В окне просмотра истории загрузки НСИ отображается дата, на которую была загружена версия НСИ и информация о загрузке. (Рисунок 24)
            <BigPicture>
              <img src={image261} alt="image261" />
              <AltPicture>Рисунок 24 – Форма синхронизации НСИ в ДЦ</AltPicture>
            </BigPicture>
          </p>
          <h5>Сопоставление объектов НСИ ПАК ЕСС с объектами ОИК СК-11</h5>
          <p>Для сопоставления объектов реестра НСИ ПАК ЕСС с объектами ОИК СК-11 в распределенной части Системы, необходимо перейти на вкладку <img src={image262} alt="image262" /> .</p>
          <p>Откроется форма редактора сопоставления объектов НСИ ПАК ЕСС и объектов ОИК СК-11. (Рисунок 25).
            <BigPicture>
              <img src={image263} alt="image263" />
              <AltPicture>Рисунок 25 – Форма синхронизации НСИ в ДЦ</AltPicture>
            </BigPicture>
          </p>
          <p>Для работы с формой пользователю необходимо сначала выставить необходимые фильтры в левом верхнем углу формы. <img src={image264} alt="image264" /> </p>
          <p>Список объектов НСИ отображается по состоянию на выбранную дату (по умолчанию выбран текущий день) для выбранного типа реестра (ГОУ, СРПГ, Сумматор) и типа объекта. Для применения фильтра необходимо нажать на кнопку <img src={image265} alt="image265" /> . Объекты типа ГОУ должны сопоставляться только в СРПГ ИА и далее должны быть распространены в СРПГ ДЦ. </p>
          <p>Для получения списка объектов из ОИК СК-11 необходимо нажать на кнопку <img src={image266} alt="image266" />  в области объектов ОИК СК-11. Загруженные объекты временно хранятся в СРПГ в соответствии с настройками заданными администратора Системы. </p>
          <p>Загрузка объектов из ОИК СК-11 может иметь несколько состояний:
            <BigPicture>
              <img src={image267} alt="image267" />
            </BigPicture>
            <BigPicture>
              <img src={image268} alt="image268" />
            </BigPicture>
            <BigPicture>
              <img src={image269} alt="image269" />
            </BigPicture>
          </p>
          <p>После успешной загрузки объектов из ОИК СК-11, загруженные объекты отображаются в интерфейсе. </p>
          <p>Для выполнения сопоставления объектов необходимо найти в списке объектов ПАК ЕСС объект, далее нажать на объект, чтобы загорелся индикатор <img src={image231} alt="image231" />  , в списке объектов ОИК СК-11 необходимо также найти объект, с которым необходимо выполнить сопоставление и нажать на кнопку  <img src={image232} alt="image232" /> (если объект еще не имеет связей) или <img src={image233} alt="image233" />   (если объект уже имеет связи). При успешном выполнении сопоставления в области объекты ПАК ЕСС у сопоставляемого объекта установится UID. </p>
          <p>Для удаления сопоставления объектов необходимо напротив объекта нажать на кнопку <img src={image234} alt="image234" /> . </p>
          <p>Для сохранения изменений на форме необходимо нажать на кнопку <img src={image235} alt="image235" /> . </p>
          <p>Для отмены изменений на форме необходимо нажать на кнопку <img src={image236} alt="image236" /></p>
          <p>Для фильтрации по сопоставленным и не сопоставленным объектам необходимо в фильтре установить значение.
            <img src={image270} alt="image270" />
          </p>
          <p>Для выгрузки результатов сопоставления необходимо нажать на <img src={image271} alt="image271" />  и выбрать из списка интересующий состав объектов.
            <img src={image272} alt="image272" /></p>
          <p> Для проверки наличия множественной связи объектов необходимо нажать на кнопку <img src={image273} alt="image273" /> . Откроется окно со списком всех дублирующих связей с UID ОИК СК-11 (Рисунок 26). </p>
          <p>В окне «Множественные связи» пункта меню «НСИ» -&gt; «Сопоставление объектов с СК-11» реализована возможность проверки выполненного сопоставления объектов СРПГ с объектами ОИК СК-11. Для запуска проверки необходимо нажать на кнопку <img src={image274} alt="image274" />  , в результате выполнения которой отобразятся сопоставленные (действующие и недействующие) объекты, имеющие ошибки. В случае отсутствия ошибок в табличной части окна «Множественные связи» отобразится уведомление «Нет данных».</p><p>Автоматически выполняемые проверки в СРПГ (недопустимые множественные связи):</p>
          <p>− множественная связь нескольких объектов СРПГ одного типа с одним объектом ОИК СК-11;</p>
          <p>− множественная связь разных типов объектов СРПГ, имеющих одинаковый UID характеристики (UID типа измерения ОИК СК-11 - MeasurementType), с одним объектом ОИК СК-11.</p>
          <p>По завершению выполнения проверки, объекты с некорректными связями будут выведены на экран с оранжевой заливкой фона. </p>
          <p>Для сброса результата проверки необходимо открыть заново окно «Множественные связи».
            <BigPicture>
              <img src={image275} alt="image275" />
              <AltPicture>Рисунок 26 – Форма множественные связи сопоставления объектов ПАК ЕСС с ОИК СК-11</AltPicture>
            </BigPicture>
          </p>
          <p>Если ранее сопоставленного объекта нет в выбранной версии НСИ, то такие объекты отображаются в виде даты вместо наименования. Дата красным цветом отображается для объектов, которые ранее действовали в НСИ, дата зеленым цветом отображается для объектов, которые еще не действуют.</p>
          <p>На форме «Множественные связи» есть возможность сбросить связи, для этого необходимо нажать на кнопку   в поле «Действия» напротив объекта. </p>
          <p>Для выгрузки результата проверки, необходимо нажать на кнопку <img src={image198} alt="image198" /> .</p>
          <p>Для сохранения изменений на форме необходимо нажать на кнопку <img src={image235} alt="image235" />  . </p>
          <p>Для отмены изменений на форме необходимо нажать на кнопку <img src={image236} alt="image236" /></p>
          <p> Для просмотра связей по объекту в области объектов ОИК СК-11 у объектов с зеленым индикатором   необходимо нажать на кнопку <img src={image213} alt="image213" />  (Рисунок 27).
            <BigPicture>
              <img src={image276} alt="image276" />
              <AltPicture>Рисунок 27 – Просмотр связей</AltPicture>
            </BigPicture>
          </p>
          <p> После нажатия на «Просмотр связей» откроется окно с установленной связью объектов по выбранному UID ОИК СК-11 (Рисунок 28).</p>
          <BigPicture>
            <img src={image277} alt="image277" />
            <AltPicture>Рисунок 28 – Форма просмотра связей объекта ОИК СК-11</AltPicture>
          </BigPicture>
          <p>На форме «Просмотр связи» есть возможность сбросить связи, для этого необходимо нажать на кнопку   в поле «Действия» напротив объекта. </p>
          <p>Для сохранения изменений на форме необходимо нажать на кнопку <img src={image235} alt="image235" /> . </p>
          <p>Для отмены изменений на форме необходимо нажать на кнопку <img src={image236} alt="image236" />.</p>
          <h5>Настройка типа генерации для сумматоров</h5>
          <p>Необходимо установить фильтр реестра объектов на форме «Сумматоры» и тип объектов «Сумматоры генерации» (Рисунок 29).
            <BigPicture>
              <img src={image278} alt="image278" />
              <AltPicture>Рисунок 29 – Функциональность сумматоров редактора сопоставления объектов</AltPicture>
            </BigPicture> </p>
          <p>В поле «Типы» необходимо нажать на   напротив необходимого объекта и далее выбрать тип генерации из списка. При изменении типа генерации, новое значение сохраняется сразу после изменения.</p>
          <p>Так как характеристики для каждого типа сумматора имеют уникальный UID СК-11, после изменения типа генерации у сумматора, необходимо выполнить настройку характеристик по сопоставленным объектам.</p>
          <h5>Поиск измерений в ОИК СК-11</h5>
          <p>Для выполнения проверки наличия измерений для записи плановых параметров в ОИК СК-11 согласно выполненным настройкам НСИ в СРПГ необходимо нажать на кнопку  <img src={image279} alt="image279" />  . По завершению поиска будет установлен зеленый индикатор  <img src={image280} alt="image280" />  и активна кнопка  <img src={image281} alt="image281" /> . </p>
          <p>Для выгрузки результатов проверки необходимо нажать на кнопку <img src={image281} alt="image281" />  и открыть выгруженный xml файл.</p>
          <h5>Настройка характеристик по сопоставленным объектам</h5>
          <p>Настройка характеристик по объектам доступна для сопоставленных объектов ПАК ЕСС. Напротив объекта необходимо нажать на шестеренку  , откроется окно с настройкой (Рисунок 30).
            <BigPicture>
              <img src={image282} alt="image282" />
              <AltPicture>Рисунок 30 – Форма настройки характеристик по объектам</AltPicture>
            </BigPicture> </p>
          <p>Необходимо выбрать тип ПГ и выставить опции напротив необходимых характеристик. </p>
          <p>Для сохранения изменений на форме необходимо нажать на кнопку <img src={image235} alt="image235" /> . </p>
          <p>Для отмены изменений на форме необходимо нажать на кнопку <img src={image236} alt="image236" />.</p>
          <Page className="block5">
            <DescriptionPage name={`working_with_scheduled_schedules`}>3.3.3. Работа с Плановыми графиками</DescriptionPage>
            <p>Данный раздел Системы предназначен для просмотра информации по полученным ПГ, выгрузке ПГ в xml, контролю распространения ПГ во внешние системы (ОИК СК-11, ёЖ-3, ИУС «СРДК», ПАК «MODES-Terminal»), резервным функциям по загрузке ПГ из файла, ручной записи ПГ во внешние системы.</p>
            <p>Резервная загрузка ПГ из файла, ручная запись ПГ доступно пользователю с ролью Технолог.</p>
            <p>Резервная загрузка ПГ из файла необходима только в случае аварийных ситуаций. Технология применяется по факту отсутствия команды акцепта ПГ в ДЦ.</p>
            <p>Для перехода к функциональности работы с Плановыми графиками необходимо нажать раздел  <img src={image250} alt="image250" />  на главной рабочей панели.</p>
            <h5>Просмотр списка ПГ</h5>
            <p>Для просмотра списка загруженных ПГ в ДЦ за выбранную дату необходимо нажать на поле даты (Рисунок 31), затем выбрать на месяце день для просмотра и нажать кнопку <img src={image208} alt="image208" /> . По умолчанию выставлена дата на текущие сутки.
              <BigPicture>
                <img src={image283} alt="image283" />
                <AltPicture>Рисунок 31 – Форма выбора даты для просмотра списка загруженных ПГ в ДЦ</AltPicture>
              </BigPicture>
            </p>
            <p>На форме отобразится список загруженных ПГ за выбранную дату. (Рисунок 32)
              <BigPicture>
                <img src={image284} alt="image284" />
                <AltPicture>Рисунок 32 – Форма просмотра загруженных ПГ в ДЦ</AltPicture>
              </BigPicture>
            </p>
            <p>В списке отображается наименование ПГ, время формирования ПГ, тип, Синхронная зона, а также статусы и время записи ПГ во внешние системы ДЦ (ОИК СК-11, ПАК «MODES-Terminal», ПАК «СРДК»).</p>
            <p>В табличной части формы «Список плановых графиков» распределенной части СРПГ отображаются следующие области:</p>
            <p>1) Список плановых графиков: </p>
            <ul>
              <li>Опция выбора ПГ <img src={image253} alt="image253" />  - при включении опции (<img src={image254} alt="image254" /> ) внизу табличной части отображается более детальная информация о ПГ (см. списки ниже – «Информация о ПГ», «Детальное распространение ПГ и действия»:</li>
              <li>Название – отображает тип и дату ПГ в формате «&lt;Тип ПГ&gt; на &lt;ДД.ММ.ГГГГ&gt;»;</li>
              <li>Время формирования – указывается время формирования ПГ в СРПГ в формате «ДД.ММ.ГГГГ чч:мм:сс»;</li>
              <li>Тип – указывается тип ПГ в формате «ПБР-&lt;номер&gt;», «УДДГ-&lt;номер&gt;», «ППБР», «ПБР», «ПЭР», «ПДГ», «ДДГ»;</li>
              <li>СЗ – указывается синхронная зона ПГ: значение «1» или «2»;</li>
              <li>Запись в ОИК СК-11 – указывается статус записи ПГ в ОИК СК-11, в случае выполнения записи указывается дата и время записи ПГ в ОИК-СК-11 в формате «ДД.ММ.ГГГГ чч:мм:сс»; </li>
              <li>Запись в ёЖ-3 – указывается статус записи ПГ в ёЖ-3, в случае выполнения записи указывается дата и время записи ПГ в ёЖ-3 в формате «ДД.ММ.ГГГГ чч:мм:сс»; </li>
              <li>Запись в Modes-Terminal – указывается статус записи ПГ в Modes-Terminal, в случае выполнения записи указывается дата и время записи ПГ в Modes-Terminal в формате «ДД.ММ.ГГГГ чч:мм:сс»; </li>
              <li>Запись в СРДК – указывается статус записи ПГ в СРДК, в случае выполнения записи указывается дата и время записи ПГ в СРДК в формате «ДД.ММ.ГГГГ чч:мм:сс».</li>
            </ul>
            <AltPicture>Примечание: статусы, отображаемые при передаче ПГ во внешние системы описаны в разделе 3.3.3.</AltPicture>
            <p>2) Информация о ПГ (доступен при включении опции (<img src={image254} alt="image254" />) напротив ПГ):</p>
            <ul>
              <li>Тип пакета – отображается тип отправляемого пакета ПГ (одно из следующих значений: ППБР, ПБР, ПЭР, ПДГ, УДДГ, ДДГ). В данном параметре также присутствует функция, позволяющая экспортировать ПГ в файл XML выбранного пользователем типа (Детальный XML, Обычный XML);</li>
              <li>Инициатор загрузки ПГ – указывается пользователь (в формате фамилия и инициалы), который инициировал загрузку ПГ. Если ПГ загружен с помощью резервной технологии, то данная информация будет отражена в данном параметре;</li>
              <li>Время получения ПГ в ДЦ – указывается дата и время получения ПГ в ДЦ в формате «ДД.ММ.ГГГГ чч:мм:сс»;</li>
              <li>Инициатор команды акцепта – в поле фиксируется пользователь (в формате фамилия и инициалы) или автоматический способ акцепта, в случае настройки автоматической команды акцепта для данного типа ПГ;</li>
              <li>Акцепт получен в ДЦ – указывается дата и время получения команды акцепта ПГ в формате «ДД.ММ.ГГГГ чч:мм:сс»;</li>
              <li>Действие – функции «Отправить ПГ (до акцепта)», «Повторить акцепт», описанные в разделе 3.3.3.</li>
            </ul>
            <p>3) Детальная информация о записи ПГ во внешние системы (доступен при включении опции (<img src={image254} alt="image254" /> ) напротив ПГ):</p>
            <ul>
              <li>Статус (заголовок таблицы) – указывается статус распространения/записи ПГ (статусы, отображаемые при передаче ПГ во внешние системы описаны в разделе 3.3.3);</li>
              <li>Внешняя система (заголовок таблицы) – указывается внешняя система, в которую выполнялась запись ПГ;</li>
              <li>Время записи (заголовок таблицы) – указывается время записи ПГ во внешнюю систему в формате «ДД.ММ.ГГГГ чч:мм:сс»;</li>
              <li>История <img src={image285} alt="image285" />   – кнопка вызывает просмотр истории записи ПГ;</li>
              <li>Действие – позволяет пользователю выполнить первичную/повторную отправку ПГ во внешнюю систему;</li>
            </ul>
            <AltPicture>Примечание: Запись ПГ типов «ПЭР», «ПДГ» во Внешние системы ёЖ-3, MODES-Terminal и ИУС «СРДК» не выполняется.</AltPicture>
            <p>После получения команды акцепта ПГ автоматически записываются в Системы (параллельно): ОИК СК-11, ПАК «MODES-Terminal», ИУС «СРДК».</p>
            <p>Статусы, отображаемые при передаче ПГ:</p>
            <ul>
              <li> <img src={image286} alt="image286" />передача ПГ нет выполнялась;</li>
              <li>  <img src={image287} alt="image287" />  ПГ записан во внешней системе;</li>
              <li>  <img src={image288} alt="image288" />  ПГ записан в ОИК СК-11, ожидается команда акцепта;</li>
              <li>   <img src={image289} alt="image289" /> при записи ПГ возникла ошибка;</li>
              <li> <img src={image290} alt="image290" /> требуется повторная запись не утвержденного ПГ по причине загрузки/акцепта ранее действующего в текущих сутках ПГ типа ПБР, УДДГ;</li>
              <li> <img src={image291} alt="image291" /> требуется повторная запись утвержденного ПГ по причине загрузки/акцепта ранее действующего в текущих сутках ПГ типа ПБР, УДДГ;</li>
            </ul>
            <p>Повторная отправка во внешние системы полученных в регламентное время ПГ доступна при возникновении ошибки по кнопке <img src={image292} alt="image292" />  напротив наименования системы.</p>
            <p>Полученные вне регламента ПГ типа ПБР [1СЗ]/ПБР [2СЗ] до получения команды акцепта могут быть записаны в ОИК СК-11 только по команде пользователя. Для возможности записи таких ПГ в детальной информации ПГ напротив ОИК СК-11 отображается кнопка «Отправить».</p>
            <p>В случае аварийных ситуаций, по факту восстановления взаимодействия экземпляров СРПГ необходимо выполнить отправку в ИА недостающих квитанций. Для отправки недостающих квитанций в ИА необходимо воспользоваться функциями <img src={image293} alt="image293" />  и <img src={image294} alt="image294" /> . При выполнении данных функций ПГ будут повторно направлены во внешние системы, а в Централизованную часть ИА будут направлены квитанции со статусом результатов отправки ПГ во внешние системы. В нормальном состоянии данные функции не используются.</p>
            <TextPage>
              На форме доступны следующие инструменты:
              <br />
              <IconContainer>
                <div>
                  <Icon width={16} name="search" />
                </div>
                - Выполнить фильтр по полю. <br />
              </IconContainer>
              <img src={image74} alt="image31" /> - Выполнить сортировку по полю . <br />
              <br />
            </TextPage>
            <p>Для выгрузки ПГ в файл формата xml, необходимо в детализации ПГ выбрать действие   и выбрать вид выгрузки обычный или детальный ПГ. В детальном ПГ выполняется выгрузка с учетом наименований объектов для быстрого поиска необходимой информации для пользователей Системы.</p>
            <h5>Резервная загрузка ПГ из файла xml</h5>
            <p>Кнопка <img src={image295} alt="image295" />  используется только в качестве резервной технологии при потере связи с ЦДУ (технология применяется по факту отсутствия команды акцепта ПГ в ДЦ). В штатном режиме работы Системы необходимо дождаться распространения ПГ из ЦДУ.</p>
            <p>При выявлении ошибки распространения из ИУС «СРПГ» ИА (ОДУ Востока) в ИУС «СРПГ» ДЦ первой (второй) синхронной зоны ЕЭС России:</p>
            <ul>
              <li>­ планового графика (до команды акцепта);</li>
              <li>­ команды акцепта планового графика, </li>
            </ul>
            <p>работник с ролью «Технолог» в ИА (ОДУ Востока) выполняет с помощью ИУС «СРПГ» повторное направление планового графика (до команды акцепта) или команды акцепта планового графика в ДЦ, в котором возникла ошибка.</p>
            <p>В случае неуспешного повторного направления команды акцепта планового графика работник с ролью «Технолог» в ИА (ОДУ Востока) выполняет формирование планового графика в формате «Обычный xml» и направляет его по электронной почте работникам роли «Технолог» в ДЦ, в которых зафиксированы проблемы получения планового графика.</p>
            <p>Работник с ролью «Технолог» в ДЦ, в которых зафиксированы проблемы получения планового графика, в регламентные сроки формирования планового графика выполняет:</p>
            <p>Ручную загрузку планового графика, полученного с помощью электронной почты из ИА (для ДЦ первой синхронной зоны ЕЭС России) или из ОДУ Востока (для ДЦ второй синхронной зоны ЕЭС России), в ИУС «СРПГ» ДЦ.</p>
            <p>Контроль автоматической фиксации команды акцепта планового графика в ИУС «СРПГ» и его записи во Внешние системы текущего ДЦ.</p>
            <p>При загрузке ПГ с помощью резервной технологии согласно дате ПГ определяется актуальная версия НСИ, согласно настройкам в конфигурации определяется СЗ экземпляра СРПГ. Актуальная НСИ фильтруется по СЗ и полученные объекты сравниваются с объектами в ПГ. Объекты, отсутствующие в НСИ с фильтром по СЗ исключаются из ПГ. Полученный ПГ пишется в БД.</p>
            <p>После успешной загрузки ПГ автоматически выполняются следующие действия последовательно: отправка до команды акцепта / команда акцепта ПГ в ОИК СК-11, отправка до команды акцепта ПГ в ёЖ-3, акцепт ПГ, отправка параллельно утвержденного ПГ в ОИК СК-11, Modes-Terminal и СРДК, при успешной записи в ОИК СК-11 выполняется отправка в ёЖ-3.</p>
            <p>Для перехода к резервной загрузке ПГ, необходимо нажать на кнопку <img src={image295} alt="image295" />  .</p>
            <p>Откроется форма для выбора файла (Рисунок 33).
              <BigPicture>
                <img src={image296} alt="image296" />
                <AltPicture>Рисунок 33 – Форма выбора файла xml для резервной загрузки ПГ</AltPicture>
              </BigPicture>
            </p>
            <p>Для выбора файла необходимо нажать на поле, затем выбрать файл или переместить файл на поле выбора файла на форме.</p>
            <p>Для загрузки выбранного файла необходимо нажать кнопку <img src={image192} alt="image192" />  , для сброса внесенных изменений нажать на кнопку <img src={image236} alt="image236" /> .</p>
            <p>В процессе загрузки ПГ проверяется формат загружаемого файла. Объекты, отсутствующие в НСИ с фильтром по СЗ исключаются из ПГ. Полученный ПГ пишется в БД.</p>
            <p>В результате загрузки ПГ выполнится автоматическая отправка не утвержденного ПГ в ОИК СК-11, отправка не утвержденного ПГ в ёЖ-3, а также выполнится автоматический акцепт ПГ и отправка параллельно утвержденного ПГ в ОИК СК-11, MODES-Terminal и СРДК, при успешной записи в ОИК СК-11 выполняется отправка в ёЖ-3. Пользователю необходимо дождаться завершения всех процессов загрузки ПГ.</p>
          </Page>

          <Page className="block 6">
            <DescriptionPage name={`event_notifications`}>3.3.4 Уведомления о событиях</DescriptionPage>
            <p>Данный раздел Системы предназначен для подписки пользователей на события и уведомления системы.</p>
            <p>Подписку на уведомления о событиях могут настроить пользователи с ролями Администратор Системы,
              Администратор НСИ, Администратор НСИ ЕСС, Технолог, Наблюдатель. </p>
            <p>Для перехода к функциональности работы с Уведомлениями необходимо нажать раздел <img src={image297}
                                                                                                    alt="image297" /> на
              главной рабочей панели.</p>
            <p>Откроется форма со списком доступных подписок для пользователя. (Рисунок 34).
              <BigPicture>
                <img src={image298} alt="image298" />
                <AltPicture>Рисунок 34 – Форма настройки подсписки на события и уведомления Системы</AltPicture>
              </BigPicture>
            </p>
            <p>Доступны следующие уведомления: </p>
            <p>&nbsp;- ИА: уведомление об успешном распространении и сохранении НСИ ЕСС во всех выбранных пользователем СРПГ
              ДЦ, уведомление об успешном распространении и сохранении результатов сопоставления объектов типа "ГОУ"
              СРПГ с объектами ОИК СК-11 во всех СРПГ ДЦ, уведомление об успешном распространении и сохранении
              технологической НСИ во всех СРПГ ДЦ, уведомление об успешном сохранении НСИ ЕСС в текущем ДЦ, уведомление
              об успешном сохранении результатов сопоставления объектов типа ${"ГОУ"} СРПГ с объектами ОИК СК-11 в
              текущем ДЦ, уведомление об успешном сохранении технологической НСИ в текущем ДЦ.</p>
            <p>&nbsp;- ОДУ, РДУ: уведомление об успешном сохранении НСИ ЕСС в текущем ДЦ, уведомление об успешном сохранении
              результатов сопоставления объектов типа "ГОУ" СРПГ с объектами ОИК СК-11 в текущем ДЦ, уведомление об
              успешном сохранении технологической НСИ в текущем ДЦ.</p>
            <p>Для подписки на уведомление, необходимо установить опцию выбора напротив уведомления.</p>
            <p>Для отписки от уведомления, необходимо снять опцию выбора напротив уведомления. </p>
            <p>Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image235} alt="image235" /> , для
              сброса внесенных изменений нажать на кнопку <img src={image236} alt="image236" /> .</p>
          </Page>

          <Page className="block7">
            <DescriptionPage name={`customizing_user_interface`}>3.4. Настройка пользовательского интерфейса</DescriptionPage>
            <p>Для изменения настроек интерфейса пользователя используется окно «Информация о пользователе».</p>
            <p>Для открытия окна «Информация о пользователе» необходимо в правом верхнем углу рабочей области Системы нажать на иконку <img src={image300} alt="image300" /> (Рисунок 35).
              <BigPicture>
                <img src={image299} alt="image299" />
                <AltPicture>Рисунок 35 – Окно «Информация о пользователе»</AltPicture>
              </BigPicture>
            </p>
            <p>В окне «Информация о пользователе» отображаются следующие элементы:</p>
            <ul>
              <li>аватар пользователя;</li>
              <li>версия ПО стенда (при наличии соответствующей настройки отображения, установленной пользователем) и экземпляр Системы (ЦДУ, наименование ОДУ или РДУ);</li>
              <li>наименование ДЦ авторизованнного пользователя;</li>
              <li>учетная запись пользователя; </li>
              <li> фамилия, имя и отчество авторизованного пользователя;</li>
              <li> роль (роли) пользователя в системе; </li>
              <li> кнопка «Настройки»;</li>
              <li> кнопка «Выход из системы».</li>
            </ul>
            <p>Для перехода на форму «Настройки пользователя» (Рисунок 36), необходимо в окне «Информация о пользователе» нажать на кнопку «Настройки». (Рисунок 36).
              <BigPicture>
                <img src={image301} alt="image301" />
                <AltPicture>Рисунок 36 – Форма «Настройки пользователя»</AltPicture>
              </BigPicture>
            </p>
          </Page>

          <Page className="block8">
            <DescriptionPage name={`changing_the_users_avatar`}>3.4.1. Изменение аватара пользователя</DescriptionPage>
            <p>Для изменения аватара пользователя, необходимо навести курсор мыши на аватар с силуэтом человека (по умолчанию) и нажать на нее. После нажатия откроется контекстное меню с выбором доступных аватаров. (Рисунок 37). Для выбора аватара требуется навести на него курсор и нажать левой кнопкой мыши.
              <BigPicture>
                <img src={image302} alt="image302" />
                <AltPicture>Рисунок 37 – Выбор аватара пользователя</AltPicture>
              </BigPicture>
            </p>
            <p>Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image235} alt="image235" />  , для сброса внесенных изменений нажать на кнопку <img src={image236} alt="image236" />  .</p>
          </Page>

          <Page className="block9">
            <DescriptionPage name={`changing_interface_theme`}>3.4.2. Изменение темы интерфейса</DescriptionPage>
            <p>На форме «Настройки пользователя» предусмотрен выбор цветовых схем пользовательского интерфейса, на выбор предлагается 4 цветовые схемы (Рисунок 38).
              <BigPicture>
                <img src={image303} alt="image303" />
                <AltPicture>Рисунок 38 – Выбор цветовой схемы пользовательского интерфейса</AltPicture>
              </BigPicture>
            </p>
            <p>Для сброса выбранной темы до темы по умолчанию импользуется кнопка <img src={image304} alt="image304" />. Тема по умолчанию хранится в отдельном конфигурационном файле <i>«config.js»</i> (детализированное описание состава конфигурационного файла представлено в разделе 8.4. Руководства системного администратора).</p>
            <p>Также в файле <i>«config.js»</i> расположена булева переменная THEME_EDIT_ENABLE (по умолчанию – true) для управления правами выбора темы пользователями. Если THEME_EDIT_ENABLE=true, то выбор пользователями темы разрешен, если THEME_EDIT_ENABLE=false, то выбор темы пользователями заблокирован. Для изменения данной настройки требуется обратиться к Администратору Системы.</p>
            <p>Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image235} alt="image235" />  , для сброса внесенных изменений нажать на кнопку <img src={image236} alt="image236" /> .</p>
          </Page>
          <Page className="block10">
            <DescriptionPage name={`displaying_application_version`} >3.4.3. Отображение версии приложения</DescriptionPage>
            <p>На форме «Настройки пользователя» присутствует опция «Показывать версию». </p>
            <p>При установке опции <img src={image305} alt="image305" /> в окне «Информация о пользователе» (Рисунок 35) отображается версия Системы. </p>
            <p>При снятии опции <img src={image306} alt="image306" />  в окне «Информация о пользователе» информация о версии Системы не отображается.</p>
            <p>Для сохранения внесенных изменений, необходимо нажать на кнопку <img src={image235} alt="image235" /> , для сброса внесенных изменений нажать на кнопку <img src={image236} alt="image236" /> .</p>
          </Page>
          <Page className="block11">
            <DescriptionPage name={`changing_appearance_drop_down_lists`}>3.4.4. Изменение вида выпадающих списков</DescriptionPage>
            <p>Для выпадающих списков интерфейса пользователя СРПГ существует возможность выбора цветовой схемы. (Рисунок 39). Для выбора вида выпадающего списка необходимо нажать на выбираемый вариант левой кнопкой мыши.
              <BigPicture>
                <img src={image307} alt="image307" />
                <AltPicture>Рисунок 39 – Форма выбора вида выпадающего списка</AltPicture>
              </BigPicture> </p>
            <p>Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image235} alt="image235" /> , для сброса внесенных изменений нажать на кнопку <img src={image236} alt="image236" /> .</p>
          </Page>
          <DescriptionPage name={`log_out_system`}>3.4.5. Выход из системы</DescriptionPage>
          <p>Для выхода из Системы в окне «Информация о пользователе» (Рисунок 35), необходимо нажать на кнопку <img src={image308} alt="image308" /> , после чего пользователю отобразится форма авторизации пользователей (Рисунок 1).</p>
          <Page>
            <TitlePage name="administration">4. АДМИНИСТРИРОВАНИЕ </TitlePage>
            <TextPage>
              Данный раздел Системы предназначен для добавления групп пользователей, зарегистрированных в AD в Систему, связи добавляемых групп пользователей с
              предопределенными ролями.
              <br />
              Добавлять группы пользователей, устанавливать связи предопределённых ролей с группами пользователей, просматривать доступный функционал для ролей может
              только пользователь с ролью Администратор Системы.
              <br />
              Для перехода к функционалу администрирования необходимо нажать раздел <img src={image111} alt="image150" /> на главной рабочей панели. Откроется раздел
              Администрирования. (Рисунок 40)
            </TextPage>
            <BigPicture>
              <img src={image112} alt="image151" />
              <AltPicture>Рисунок 40 - Интерфейс по Администрированию групп пользователей в Системе</AltPicture>
            </BigPicture>
          </Page>
          <Page>
            <DescriptionPage name="unknown"> Изменение связи роли с группой AD</DescriptionPage>
            <TextPage>
              Для изменения связи роли с группой AD в Системе, необходимо нажать в строке с наименованием роли кнопку в виде карандаша
              <img src={image113} alt="image152" /> . Отроется форма для добавления изменения связи роли с группой AD. (Рисунок 41)
            </TextPage>
            <BigPicture>
              <img src={image114} alt="image153" />
              <AltPicture>Рисунок 41 - Форма добавления групп пользователей</AltPicture>
            </BigPicture>
            <TextPage>
              Для добавления группы в Систему необходимо нажать на кнопку в виде плюса в строке с названием группы в столбце «Действие».
              <br />
              Группа переместится в верхнюю часть формы.
              <br />
              После выбора роли, на форме станет активна кнопка <img src={image18} alt="save" /> .
              <br />
              Для сохранения связи группы с ролью и добавления группу в Системы, необходимо нажать кнопку <img src={image18} alt="save" /> , для сброса нажать кнопку
              <img src={image58} alt="cancel" /> .
              <br />
              После сохранения, группа пользователей будет отображается на форме списка групп.
              <br />
              Для подробного просмотра списка связанных с ролью групп AD, необходимо нажать на кнопку <img src={image115} alt="copy" /> . Откроется форма с подробным
              перечнем вложенных групп AD, связанных с ролью. (Рисунок 42)
            </TextPage>
            <BigPicture>
              <img src={image116} alt="image154" />
              <AltPicture>Рисунок 42 - Форма списка связанных групп пользователей с ролью</AltPicture>
            </BigPicture>
            <TextPage>
              Для просмотра списка функций роли в Системе, необходимо нажать в строке с наименованием роли кнопку <img src={image117} alt="user" /> .
              <br />
              Откроется форма со списком предустановленных ролей в Системе. (Рисунок 43)
            </TextPage>
            <BigPicture>
              <img src={image118} alt="image155" />
              <AltPicture>Рисунок 43 - Форма просмотра списка ролей</AltPicture>
            </BigPicture>
            <TextPage>Для просмотра доступных функций роли, необходимо нажать на название роли в списке ролей.</TextPage>
          </Page>
          <Page>
            <TitlePage name="system_settings">5. НАСТРОЙКИ СИСТЕМЫ</TitlePage>
            <DescriptionPage name="general_settings">5.1. Общие настройки </DescriptionPage>
            <TextPage>
              Данный раздел Системы предназначен для настроек системы по взаимодействию с внешними системами и глубины хранения данных.
              <br />
              Изменять настройки системы может только пользователь с ролью Администратор Системы.
              <br />
              Для перехода к функционалу администрирования необходимо нажать раздел <img src={image119} alt="image156" /> на главной рабочей панели.
              <br />
              Откроется раздел «Настройки» с вкладками на рабочей панели <img src={image120} alt="image157" /> . (Рисунок 44)
            </TextPage>
            <BigPicture>
              <img src={image121} alt="image158" />
              <AltPicture>Рисунок 44 - Интерфейс настроек Системы</AltPicture>
            </BigPicture>
          </Page>
          <Page>
            <DescriptionPage name="setting_the_data_storage_depth">5.1.1. Настройка глубины хранения данных</DescriptionPage>
            <TextPage>
              Для перехода к настройкам хранения данных, необходимо нажать на рабочей панели вкладку <img src={image122} alt="image159" /> , затем в правой части нажать
              на раздел <img src={image123} alt="image160" /> .
              <br />
              Откроется форма настройки глубины хранения данных. (Рисунок 45)
            </TextPage>
            <BigPicture>
              <img src={image124} alt="image161" />
              <AltPicture>Рисунок 45 - Форма настроек глубины хранения данных</AltPicture>
            </BigPicture>
            <TextPage>
              Глубина хранения данных задается для ПГ, НСИ и Журналов системы. Глубина задается в месяцах.
              <br />
              На форме доступны следующие инструменты: <br />
              <IconContainer>
                <div>
                  <Icon width={16} name="search" />
                </div>
                - Выполнить фильтр по полю.
              </IconContainer>
              Для изменения периода хранения, необходимо указать количество месяцев.
              <br />
              Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image18} alt="save" /> , для сброса нажать кнопку <img src={image58} alt="cancel" />{" "}
              .
            </TextPage>
          </Page>
          <Page>
            <DescriptionPage name="settings_for_interaction_with_external_systems">5.1.2. Настройки взаимодействия с внешними системами</DescriptionPage>
            <TextPage>
              Для перехода к настройкам взаимодействия с внешними системами, необходимо нажать рабочей панели вкладку <img src={image122} alt="image162" /> , затем в
              правой части нажать на раздел <img src={image125} alt="image163" /> .
              <br />
              Откроется форма настроек взаимодействия с внешними системами (Рисунок 46)
            </TextPage>
            <BigPicture>
              <img src={image126} alt="image164" />
              <AltPicture>Рисунок 46 - Форма настроек взаимодействия с внешними системами</AltPicture>
            </BigPicture>
          </Page>
          <Page>
            <TitlePage>Настройка взаимодействия с ПАК «ЕСС»</TitlePage>
            <TextPage>
              Настройка взаимодействия с ПАК «ЕСС» выполняется только в Централизованной части экземпляра СРПГ ИА.
              <br />
              Для перехода к настройкам интеграции с ПАК «ЕСС», необходимо выполнить переключение на Централизованную часть в экземпляре СРПГ ИА, далее в настройках
              взаимодействия с внешними системами выбрать вкладку <img src={image127} alt="image165" /> .
              <br />
              Откроется форма настройки интеграции с ПАК «ЕСС». (Рисунок 47)
            </TextPage>
            <BigPicture>
              <img src={image128} alt="image166" />
              <AltPicture>Рисунок 47 - Форма настроек интеграции с ПАК ЕСС</AltPicture>
            </BigPicture>
            <TextPage>
              В поле ввода «Адрес сервиса» необходимо задать адрес сервиса ПАК «ЕСС», который уточняется у администратора ПАК «ЕСС». Адрес задается в формате
              {"http://<адрес сервиса ПАК «ЕСС»:порт>/api/WebApi/startTask"}
              <br />
              В поле ввода «Логин» необходимо указать имя сервисной УЗ Системы, созданной в AD для экземпляра СРПГ ИА.
              <br />
              В поле «Пароль» необходимо указать пароль сервисной УЗ Системы, созданной в AD для экземпляра СРПГ ИА.
              <br />
              В поле «Количество повторных попыток» задать количество повторных попыток запросов к ПАК «ЕСС» в случае ошибки подключения.
              <br />
              В поле «Интервал повторных попыток» задается временной интервал в секундах между повторными попытками подключения.
              <br />
              Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image18} alt="save" /> , для сброса нажать кнопку <img src={image58} alt="cancel" />{" "}
              .
              <br />
              Для проверки соединения с указанными настройками с ПАК «ЕСС», необходимо нажать на кнопку <img src={image129} alt="test" /> .
              <br />
              После нажатия, будет выполнен тестовый запрос к внешней системе. По результатам тестирования выведется уведомление с информацией по результатам проверки.
            </TextPage>
          </Page>
          <Page>
            <TitlePage>Настройка взаимодействия с ПАК «ОпАМ»</TitlePage>
            <TextPage>
              Настройка взаимодействия с ПАК «ОпАМ» выполняется только в Централизованной части экземпляров СРПГ ИА и СРПГ ОДУ Востока.
              <br />
              Для перехода к настройкам интеграции с ПАК «ОпАМ», необходимо выполнить переключение на Централизованную часть, далее в настройках взаимодействия с внешними
              системами выбрать вкладку <img src={image130} alt="image167" /> .
              <br />
              Откроется форма настройки интеграции с ПАК «ОпАМ». (Рисунок 48)
            </TextPage>
            <BigPicture>
              <img src={image131} alt="image168" />
              <AltPicture>Рисунок 48 - Форма настроек интеграции с ПАК «ОпАМ»</AltPicture>
            </BigPicture>
            <TextPage>
              В поле ввода «Адрес сервиса» необходимо задать адрес сервиса ПАК «ОпАМ», который уточняется у администратора ПАК «ОпАМ». Адрес задается в формате{" "}
              {"http://<адрес сервиса ПАК «ОпАМ»:порт>/api/WebApi/startTask"}
              <br />
              В поле ввода «Логин» необходимо указать имя сервисной УЗ Системы, созданной в AD для экземпляра СРПГ ИА.
              <br />
              В поле «Пароль» необходимо указать пароль сервисной УЗ Системы, созданной в AD для экземпляра СРПГ ИА.
              <br />
              В поле «Количество повторных попыток» задать количество повторных попыток запросов к ПАК «ОпАМ» в случае ошибки подключения.
              <br />
              В поле «Интервал повторных попыток» задается временной интервал в секундах между повторными попытками подключения.
              <br />
              Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image18} alt="save" /> , для сброса нажать кнопку <img src={image58} alt="cancel" />{" "}
              .
              <br />
              Для проверки соединения с указанными настройками с ПАК «ОпАМ», необходимо нажать на кнопку <img src={image129} alt="test" /> .
              <br />
              После нажатия, будет выполнен тестовый запрос к внешней системе. По результатам тестирования выведется уведомление с информацией по результатам проверки.
            </TextPage>
          </Page>
          <Page>
            <TitlePage>Настройка взаимодействия с Мегаточкой</TitlePage>
            <TextPage>
              Настройка взаимодействия с Мегаточкой выполняется только в Централизованной части экземпляров СРПГ ИА и СРПГ ОДУ Востока. Для перехода к настройкам
              интеграции с Мегаточкой, необходимо выполнить переключение на Централизованную часть, далее в настройках взаимодействия с внешними системами выбрать вкладку{" "}
              <img src={image132} alt="image169" /> .
              <br />
              Откроется форма настройки интеграции с Мегаточкой. (Рисунок 49)
            </TextPage>
            <BigPicture>
              <img src={image133} alt="image170" />
              <AltPicture>Рисунок 49 - Форма настроек интеграции с Мегаточкой</AltPicture>
            </BigPicture>
            <TextPage>
              Посредствам кнопки <img src={image134} alt="image171" /> добавить в поля для ввода «Адрес сервиса» и задать адреса серверов приложений Windows экземпляра
              СРПГ текущего ДЦ (для взаимодействия с Мегаточкой). В поле «Адрес сервиса» указывается имя сервера приложений или ip адрес.
              <br />В полях ввода напротив названий плановых графиков задаются шаблон сетевого пути до хранилища файлов Мегаточки вида{" "}
              {"«\\Clubr-cduPDGMPT\\vostok и шаблон имени файла планового графика вида {dd}{MM}{yy}-17_outLin.mpt.»"}
              <br />
              В поле «Количество повторных попыток» задать количество повторных попыток запросов сервиса к Мегаточки.
              <br />
              В поле «Интервал повторных попыток» задается временной интервал в секундах между повторными попытками подключения.
              <br />
              Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image18} alt="save" /> , для сброса нажать кнопку <img src={image58} alt="cancel" />{" "}
              .
              <br />
              Для проверки соединения с указанными настройками к Мегаточке, необходимо нажать на кнопку <img src={image129} alt="test" /> .
              <br />
              После нажатия, будет выполнен тестовый запрос к внешней системе. По результатам тестирования выведется уведомление с информацией по результатам проверки.
            </TextPage>
          </Page>
          <Page>
            <TitlePage>Настройка взаимодействия с ОИК СК-11</TitlePage>
            <TextPage>
              Настройка взаимодействия с ОИК СК-11 выполняется в Централизованной части экземпляра СРПГ ИА, ОДУ Востока и Распределенных частях Системы ОДУ, РДУ.
              <br />
              Для перехода к настройкам интеграции с ОИК СК-11 в настройках взаимодействия с внешними системами выбрать вкладку <img src={image135} alt="image172" /> .
              <br />
              Откроется форма настройки интеграции с ОИК СК-11.(Рисунок 50)
            </TextPage>
            <BigPicture>
              <img src={image136} alt="image173" />
              <AltPicture>Рисунок 50 - Форма настроек интеграции с ОИК СК-11</AltPicture>
            </BigPicture>
            <TextPage>
              В поле ввода «Адрес сервиса» необходимо задать адреса сервисов ОИК СК-11 основного и резервного, которые уточняются у администратора ОИК СК-11. Адрес
              задается в формате {"https://<адрес сервиса ОИК СК-11:порт>/api/public/core/v2.1"}
              <br />В поле ввода «Логин» необходимо указать имя сервисной УЗ Системы, созданной в AD для экземпляра СРПГ.
              <br />В поле «Пароль» необходимо указать пароль сервисной УЗ Системы, созданной в AD для экземпляра СРПГ.
              <br />В поле «Количество повторных попыток» задать количество повторных попыток запросов к ОИК СК-11 в случае ошибки подключения.
              <br />В поле «Интервал повторных попыток» задается временной интервал в секундах между повторными попытками подключения.
              <br />
              Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image18} alt="save" /> , для сброса нажать кнопку <img src={image58} alt="cancel" />.
              Для проверки соединения с указанными настройками с ОИК СК-11 необходимо нажать на кнопку <img src={image129} alt="test" /> .
              <br />
              После нажатия, будет выполнен тестовый запрос к внешней системе. По результатам тестирования выведется уведомление с информацией по результатам проверки.
              <br />
              Типовые ошибки ОИК СК-11.
            </TextPage>
            <Table>
              <tr>
                <TD>Ошибка</TD>
                <TD>Рекомендации</TD>
              </tr>
              <tr>
                <TD>Не найдены измерения для публикации с uid ПГ = {"'10000056-0000-0000-c000-0000006d746c'"}</TD>
                <TD>
                  В составе ПГ отправляются измерения, которые отсутствуют в ОИК СК-11 в полном составе. Необходимо проверить, какие объекты настроены для отправки в
                  Редакторе сопоставления объектов (объект должен быть сопоставлен с UID ОИК СК-11, у объекта должны быть включены характеристики для отправляемого типа
                  ПГ). Необходимо проверить наличие измерений в модели ОИК СК-11.
                </TD>
              </tr>
              <tr>
                <TD>{"Error Rtdb Access Denied"}</TD>
                <TD>
                  Ошибка связана с отсутствием доступа на запись статусов ПГ. В ошибке указан UID ({`uid:"d7cd117d-3b30-4c89-a8c3-87236ce6a6de"`}). Необходимо скопировать
                  UID из ошибки и проверить настройки в ОИК СК-11 на доступ к записи значений.
                </TD>
              </tr>
              <tr>
                <TD>Rtdb Uid Not Found</TD>
                <TD>
                  Ошибка связана с отсутствием ячейки с UID в ОИК СК-11 для записи значения. В ошибке UID указан измерения ({`uid:"bedbfbd9-cb5b-4b7f-9be2-99c3e544359c"`}
                  ). Необходимо скопировать UID из ошибки и проверить в Навигаторе данных ОИК СК-11 наличие измерения (ячейки), куда необходимо выполнить запись значения.
                  При отсутствии ячейки с таким UID, необходимо ее создать.
                </TD>
              </tr>
            </Table>
          </Page>
          <Page>
            <TitlePage>Настройка взаимодействия с почтовым сервером</TitlePage>
            <TextPage>
              Настройка взаимодействия с почтовым сервером выполняется в Централизованной части экземпляра СРПГ ИА, ОДУ Востока и Распределенных частях Системы ОДУ, РДУ.
              <br />
              Для перехода к настройкам интеграции с почтовым сервисом в настройках взаимодействия с внешними системами выбрать вкладку{" "}
              <img src={image137} alt="image174" /> .
              <br />
              Откроется форма настройки интеграции с почтовым сервером. (Рисунок 51)
            </TextPage>
            <BigPicture>
              <img src={image138} alt="image175" />
              <AltPicture>Рисунок 51 - Форма настроек интеграции с почтовым сервером</AltPicture>
            </BigPicture>
            <TextPage>
              В поле ввода «Адрес сервиса» необходимо задать адрес почтового сервера.
              <br />
              В поле ввода «Порт», указать порт, для подключения к серверу исходящей почты SMTP.
              <br />
              В поле ввода «Логин» необходимо указать имя сервисной УЗ Системы, созданной в AD для экземпляра СРПГ.
              <br />
              В поле «Пароль» необходимо указать пароль сервисной УЗ Системы, созданной в AD для экземпляра СРПГ.
              <br />
              Из списка «Шифрование» выбрать один из вариантов шифрования: TSL, SSL, нет.
              <br />
              В поле «Количество повторных попыток» задать количество повторных попыток запросов к почтовому серверу в случае ошибки подключения.
              <br />
              В поле «Интервал повторных попыток» задается временной интервал в секундах между повторными попытками подключения.
              <br />
              Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image18} alt="save" /> , для сброса нажать кнопку <img src={image58} alt="cancel" />{" "}
              .
              <br />
              Для проверки соединения с указанными настройками с почтовым сервером необходимо нажать на кнопку <img src={image129} alt="test" /> .
              <br />
              После нажатия, будет выполнен тестовый запрос к внешней системе. По результатам тестирования выведется уведомление с информацией по результатам проверки.
            </TextPage>
          </Page>
          <Page>
            <TitlePage>Настройка взаимодействия с ПАК «MODES-Terminal»</TitlePage>
            <TextPage>
              Настройка взаимодействия с ПАК «MODES-Terminal» выполняется в Распределенных частях экземпляров СРПГ ИА, ОДУ, РДУ.
              <br />
              Для перехода к настройкам интеграции с ПАК «MODES-Terminal», необходимо выполнить переключение на Распределенную часть, далее в настройках взаимодействия с
              внешними системами выбрать вкладку <img src={image139} alt="image176" /> .
              <br />
              Откроется форма настройки интеграции с ПАК «MODES-Terminal», (Рисунок 52)
            </TextPage>
            <BigPicture>
              <img src={image140} alt="image177" />
              <AltPicture>Рисунок 52 - Форма настроек интеграции с ПАК «MODES-Terminal»</AltPicture>
            </BigPicture>
            <TextPage>
              В поле ввода «Адрес сервиса» необходимо задать адрес сервиса ПАК «MODES-Terminal», который уточняется у администратора ПАК «MODES-Terminal» в формате{" "}
              {"http://<адрес сервиса ПАК «MODES-Terminal»:Порт>/SetPlanEx"}
              <br />В поле «Кодировка» задается значение:
              <ul>
                <li>«ПАК ЕСС» (по умолчанию). Запись ПГ в ПАК «MODES-Terminal» будет выполняться по ID ЕСС.</li>
                <li>• ОИК СК-11. Запись ПГ в ПАК «MODES-Terminal» будет выполняться по UID СК-11.</li>
              </ul>
              <br />
              В поле «Количество повторных попыток» задать количество повторных попыток запросов к ПАК ПАК «MODES-Terminal», в случае ошибки подключения.
              <br />
              В поле «Интервал повторных попыток» задается временной интервал в секундах между повторными попытками подключения.
              <br />
              Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image18} alt="save" /> , для сброса нажать кнопку <img src={image58} alt="cancel" />{" "}
              .
              <br />
              Для проверки соединения с указанными настройками с ПАК «MODES-Terminal», необходимо нажать на кнопку <img src={image129} alt="test" /> .
              <br />
              После нажатия, будет выполнен тестовый запрос к внешней системе. По результатам тестирования выведется уведомление с информацией по результатам проверки.
            </TextPage>
          </Page>
          <Page>
            <TitlePage>Настройка взаимодействия с ИУС «СРДК»</TitlePage>
            <TextPage>
              Настройка взаимодействия с ИУС «СРДК» выполняется в Распределенных частях экземпляров СРПГ ИА, ОДУ, РДУ.
              <br />
              Для перехода к настройкам интеграции с ИУС «СРДК» необходимо выполнить переключение на Распределенную часть, далее в настройках взаимодействия с внешними
              системами выбрать вкладку <img src={image141} alt="image178" /> .
              <br />
              Откроется форма настройки интеграции с ИУС «СРДК» (Рисунок 53)
            </TextPage>
            <BigPicture>
              <img src={image142} alt="image179" />
              <AltPicture>Рисунок 53 - Форма настроек интеграции с ИУС «СРДК»</AltPicture>
            </BigPicture>
            <TextPage>
              В поле ввода «Адрес сервиса» необходимо задать адрес сервиса Распределенной части ИУС «СРДК», который уточняется у администратора ИУС «СРДК» в формате
              {"http://<адрес сервиса ИУС «СРДК»>/api/v1/pg/saveDataPG"}
              <br />В поле ввода «Логин» необходимо указать имя сервисной УЗ Системы, созданной в AD для экземпляра СРПГ.
              <br />В поле «Пароль» необходимо указать пароль сервисной УЗ Системы, созданной в AD для экземпляра СРПГ.
              <br />В поле «Количество повторных попыток» задать количество повторных попыток запросов к ИУС «СРДК», в случае ошибки подключения.
              <br />В поле «Интервал повторных попыток» задается временной интервал в секундах между повторными попытками подключения.
              <br />
              Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image18} alt="save" /> , для сброса нажать кнопку <img src={image58} alt="cancel" />{" "}
              .
              <br />
              Для проверки соединения с указанными настройками с ИУС «СРДК», необходимо нажать на кнопку <img src={image129} alt="test" /> .
              <br />
              После нажатия, будет выполнен тестовый запрос к внешней системе. По результатам тестирования выведется уведомление с информацией по результатам проверки.
            </TextPage>
          </Page>
          <Page>
            <TitlePage> Подключение к ЕСМ</TitlePage>
            <TextPage>
              Подключение к ЕСМ осуществляется согласно инструкции по взаимодействию с ЕСМ предоставляемой администратором ЕСМ.
              <br />
              Для интеграции с ЕСМ нелюдимо:
              <br />
              1. Установить пакет snmpd.
              <br />
              2. sudo apt update && sudo apt install snmpd
              <br />
              3. Произвести настройки уведомлений относительно: доступности портов сервисов, нагрузки на процессор, нагрузки на систему хранения, объема свободного
              дискового пространства и объёма свободной оперативной памяти для каждого из серверов системы. Пороговые значения отражены в Руководства администратора
              системы в главе «Штатная работа сервиса».
              <br />
              4. Экспортировать в систему ЕСМ mib файл системы СРПГ. Для этого необходимо зайти доступный по &nbsp;
              <a href="https://asdu-fpa-gitlab.cdu.so/" target="_blank" rel="noreferrer">
                ссылке
              </a>
              &nbsp; , авторизоваться. После зайти по &nbsp;
              <a href="https://asdu-fpa-gitlab.cdu.so/srpg/config/-/blob/1.0.10/install/esm-mib/SRPG.mib" target="_blank" rel="noreferrer">
                ссылке
              </a>
              &nbsp; .
            </TextPage>
          </Page>
          <Page>
            <DescriptionPage name="pg_settings">5.2. Настройки ПГ</DescriptionPage>
            <TextPage>Данный раздел Системы предназначен для настроек загрузки и записи ПГ.</TextPage>
            <DescriptionPage name="pbr"> 5.2.1. Настройка Номер ПБР [1СЗ]</DescriptionPage>
            <TextPage>
              Настройка выполняется в Централизованной части экземпляра СРПГ ИА. В Распределенных частях экземпляров СРПГ ОДУ, РДУ настройка отображается в режиме
              просмотра.
              <br />
              Для перехода к настройкам номера ПБР, необходимо нажать рабочей панели вкладку <img src={image143} alt="image180" /> , затем в правой части нажать на раздел
              <img src={image144} alt="image181" /> .
              <br />
              Откроется форма с настройками номера ПБР. (Рисунок 54)
            </TextPage>
            <BigPicture>
              <img src={image145} alt="image182" />
              <AltPicture>Рисунок 54 - Форма настроек номера ПБР</AltPicture>
            </BigPicture>
            <TextPage>
              На форме доступны следующие инструменты:
              <br />
              <IconContainer>
                <div>
                  <Icon width={16} name="search" />
                </div>
                - Выполнить фильтр по полю.
              </IconContainer>
              <br />
              <img src={image74} alt="image31" /> - Выполнить сортировку по полю . <br />
              <br />
              Для изменения часа начала действия ПБР, необходимо выбрать время напротив каждого номера ПБР.
              <br />
              Для изменения признака перехода на следующий день, необходимо поставить отметку выбора в чек-боксе строки номера ПБР.
            </TextPage>
            <BigPicture>
              <img src={image146} alt="image183" />
            </BigPicture>
            Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image18} alt="save" /> , для сброса нажать кнопку <img src={image58} alt="cancel" /> .
          </Page>
          <Page>
            <DescriptionPage name="uddg">5.2.2. Настройка Номер ПБР [2СЗ]</DescriptionPage>
            <TextPage>
              Настройка выполняется в Централизованной части экземпляра СРПГ ИА. В Распределенных частях экземпляров СРПГ РДУ Востока, а также в Централизованной части
              экземпляра ОДУ Востока настройка отображается в режиме просмотра.
              <br />
              Для перехода к настройкам Номера ПБР [2СЗ], необходимо нажать рабочей панели вкладку <img src={image147} alt="image184" /> , затем в правой части нажать на
              раздел <img src={image148} alt="image185" /> .
              <br />
              Откроется форма с настройками Номера ПБР [2СЗ]. (Рисунок 55)
            </TextPage>
            <BigPicture>
              <img src={image149} alt="image186" />
              <AltPicture>Рисунок 55 - Форма настроек Номера ПБР [2СЗ]</AltPicture>
            </BigPicture>
            <TextPage>
              На форме доступны следующие инструменты:
              <br />
              <IconContainer>
                <div>
                  <Icon width={16} name="search" />
                </div>
                - Выполнить фильтр по полю.
              </IconContainer>
              <br />
              <img src={image74} alt="image31" /> - Выполнить сортировку по полю . <br />
              Для изменения часа начала действия ПБР [2СЗ], необходимо выбрать время напротив каждого Номера ПБР [2СЗ]
              <br />
              Для изменения признака перехода на следующий день, необходимо поставить отметку выбора в чек-боксе строки Номера ПБР [2СЗ].
            </TextPage>
            <BigPicture>
              <img src={image146} alt="image183" />
            </BigPicture>
            <TextPage>
              Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image18} alt="save" /> , для сброса нажать кнопку <img src={image58} alt="cancel" />{" "}
              .
            </TextPage>
          </Page>
          <Page>
            <DescriptionPage name="setting_up_pg_characteristics">5.2.3. Настройка характеристик ПГ</DescriptionPage>
            <TextPage>
              Настройка выполняется в Централизованной части экземпляра СРПГ ИА. В Распределенных частях экземпляров СРПГ ОДУ, РДУ, а также в Централизованной части
              экземпляра ОДУ Востока настройка отображается в режиме просмотра.
              <br />
              Для перехода к настройкам характеристик, необходимо нажать на рабочей панели вкладку <img src={image150} alt="image187" /> , затем в правой части нажать на
              раздел <img src={image151} alt="image188" /> .
              <br />
              Откроется форма настройки характеристик ПГ. (Рисунок 56)
            </TextPage>
            <BigPicture>
              <img src={image152} alt="image189" />
              <AltPicture>Рисунок 56 - Форма настройки характеристик ПГ </AltPicture>
            </BigPicture>
            <TextPage>
              Для добавления новой характеристики, полученной из ПАК «ОпАМ», необходимо нажать на кнопку в виде плюса напротив названия типа объекта
              <img src={image153} alt="plus" /> .
              <br />
              Откроется форма добавления характеристики по типу объекта.
            </TextPage>
            <BigPicture>
              <img src={image154} alt="image190" />
            </BigPicture>
            <TextPage>
              Необходимо выбрать способ создания характеристики:
              <br />- Характеристики из ПАК «ОпАМ».
              <br />- Ручной ввод.
              <br />
              Из списка характеристик необходимо выбрать новую характеристику, затем указать в поле названия характеристики – название характеристики.
              <br />
              Для добавления, необходимо нажать кнопку <img src={image155} alt="add" /> , для сброса нажать кнопку <img src={image58} alt="cancel" /> .
              <br />
              Для изменения значения, необходимо нажать на кнопку в виде карандаша <img src={image113} alt="edit" /> в строке с типом объекта, затем внести изменения.
              <br />
              Для определения типа планового графика, в составе которого будет характеристика, необходимо проставить опции у требуемого типа ПГ.
            </TextPage>
            <BigPicture>
              <img src={image156} alt="image191" />
            </BigPicture>
            <TextPage>
              Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image18} alt="save" /> , для сброса нажать кнопку <img src={image58} alt="cancel" />{" "}
              .
              <br />
              Для создания и настройки типов сумматоров агрегированной плановой генерации, необходимо напротив сумматора нажать на кнопку «<img
              src={image157}
              alt="t"
            />{" "}
              ».
              <br />
              Пример модального окна настройки типов сумматоров плановой агрегированной генерации:
            </TextPage>
            <BigPicture>
              <img src={image158} alt="image192" />
            </BigPicture>
            <TextPage>
              В открывшемся модальном окне доступны следующие функции:
              <br />- Добавить тип сумматора. Необходимо нажать на кнопку <img src={image155} alt="add" /> , ввести название типа в произвольной форме и далее нажать на
              кнопку
              <img src={image155} alt="add" /> . В результате успешного добавления типа сумматора в списке типов появится новое значение.
              <br />- Редактировать тип сумматора. Доступно редактирование название типа сумматора. Необходимо нажать на кнопку «<img src={image100} alt="edit" />»
              напротив типа сумматора, внести изменения и нажать на кнопку <img src={image159} alt="apply" /> .
              <br />
              - Удалить тип сумматора. Необходимо нажать на кнопку «<img src={image160} alt="trash" />» напротив типа сумматора и подтвердить удаление по кнопке
              <img src={image161} alt="delete" /> .
              <br />- Добавить характеристику типа сумматора. Необходимо напротив необходимого типа сумматора нажать на кнопку «<img src={image153} alt="plus" />
              », выбрать из выпадающего списка характеристику, указать UID СК-11 и далее нажать на кнопку <img src={image159} alt="apply" /> . В результате успешного
              добавления характеристики в списке характеристик для выбранного типа появится новое значение.
              <br />- Редактировать характеристику типа сумматора. Доступно редактирование характеристики и UID СК-11. Необходимо нажать на кнопку «
              <img src={image100} alt="edit" />» напротив характеристики, внести изменения и нажать на кнопку <img src={image159} alt="apply" />.
              <br />
              - Удалить характеристику типа сумматора. Необходимо нажать на кнопку «<img src={image160} alt="trash" />» напротив характеристики и подтвердить удаление по
              кнопке <img src={image161} alt="delete" />.
              <br />
              Все изменения выполненные в модальном окне сохраняются по кнопке <img src={image18} alt="save" />.
            </TextPage>
          </Page>
          <Page>
            <DescriptionPage name="empty_data">5.2.4. Настройка константы отсутствия данных </DescriptionPage>
            <TextPage>
              Настройка выполняется в Централизованной части экземпляра СРПГ ИА. В Распределенных частях экземпляров СРПГ ОДУ, РДУ, а также в Централизованной части
              экземпляра ОДУ Востока настройка отображается в режиме просмотра.
              <br />
              Для перехода к настройкам константы отсутствия данных, необходимо нажать рабочей панели вкладку <img src={image150} alt="image192" /> , затем в правой части
              нажать на раздел <img src={image162} alt="image193" /> .
              <br />
              Откроется форма настройки отсутствия данных. (Рисунок 57)
            </TextPage>
            <BigPicture>
              <img src={image163} alt="image194" />
              <AltPicture>Рисунок 57 - Форма настройки константы отсутствия данных </AltPicture>
            </BigPicture>
            <TextPage>
              Для добавления значения константы по типу объекта, необходимо нажать на кнопку <img src={image155} alt="add" /> .
              <br />
              Откроется форма добавления значения константы по типу объекта.
            </TextPage>
            <BigPicture>
              <img src={image164} alt="image195" />
            </BigPicture>
            <TextPage>
              Для добавления характеристики, необходимо нажать кнопку <img src={image155} alt="add" /> , для сброса нажать кнопку <img src={image58} alt="cancel" /> .
              <br />
              Для изменения значения, необходимо нажать на кнопку «<img src={image100} alt="edit" />» в строке с типов объекта.
              <br />
              Откроется форма изменения значения константы для выбранного типа объекта.
            </TextPage>
            <BigPicture>
              <img src={image165} alt="image196" />
            </BigPicture>
            <TextPage>
              Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image18} alt="save" /> , для сброса нажать кнопку <img src={image58} alt="cancel" />{" "}
              .
              <br />
              Для удаления заданной константы по типу объекта необходимо нажать на кнопку «<img src={image160} alt="trash" />» в строке с типом объекта.
            </TextPage>
          </Page>
          <Page>
            <DescriptionPage name="setting_up_pg_loading">5.2.5. Настройка загрузки ПГ</DescriptionPage>
            <TextPage>
              Настройка выполняется в Централизованной части экземпляров СРПГ ИА, ОДУ Востока.
              <br />
              Для перехода к настройкам номера ПБР, необходимо нажать на рабочей панели вкладку <img src={image147} alt="image197" /> , затем в правой части нажать на
              раздел
              <img src={image166} alt="image198" /> .
              <br />
              Откроется форма настройки загрузки типов ПГ, способа их акцепта и параметры смещения графика при загрузке. (Рисунок 58)
            </TextPage>
            <BigPicture>
              <img src={image167} alt="image199" />
              <AltPicture>Рисунок 58 - Форма настройки способа акцепта ПГ и параметров смещения</AltPicture>
            </BigPicture>
            <TextPage>
              На форме доступны следующие инструменты:
              <br />
              <IconContainer>
                <div>
                  <Icon width={16} name="search" />
                </div>
                - Выполнить фильтр по полю. <br />
              </IconContainer>
              <img src={image74} alt="image31" /> - Выполнить сортировку по полю . <br />
              <br />
              Для настройки смещения от плановой даты, на которое будет выполняться поиск графика при загрузке из внешней системы необходимо в поле «Смещение» выбрать из
              списка значение.
            </TextPage>
            <BigPicture>
              <img src={image168} alt="image200" />
            </BigPicture>
            <TextPage>
              Для настройки способа акцепта необходимо в поле «Способ акцепта», выбрать из списка значение ручной или автоматический, а также указать время в секундах
              автоматического выполнения акцепта.
            </TextPage>
            <BigPicture>
              <img src={image169} alt="image201" />
              <AltPicture>Рисунок 59 - Список выбора способов акцепта ПГ</AltPicture>
            </BigPicture>
            <TextPage>Для настройки расчета сумматоров необходимо поставить отметку выбора в чек-боксе строки типа ПГ.</TextPage>
            <BigPicture>
              <img src={image170} alt="image202" />
            </BigPicture>
            <TextPage>
              Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image18} alt="save" /> , для сброса нажать кнопку <img src={image58} alt="cancel" />{" "}
              .
            </TextPage>
          </Page>
          <Page>
            <DescriptionPage name="configuring_the_composition_of_transmitted_graph_data_in_MODES_Terminal">
              5.2.6. Настройка состава передаваемых данных графика в MODES-Terminal
            </DescriptionPage>
            <TextPage>
              Настройка выполняется в Централизованной части экземпляра СРПГ ИА. В Распределенных частях экземпляров СРПГ ОДУ, РДУ, а также в Централизованной части
              экземпляра ОДУ Востока настройка отображается в режиме просмотра.
              <br />
              Для перехода к настройкам константы отсутствия данных, необходимо нажать рабочей панели вкладку <img src={image147} alt="image203" /> , затем в правой части
              нажать на раздел <img src={image171} alt="image204" /> .
              <br />
              Откроется форма настройки состава ПГ, передаваемая в ПАК «MODES-Terminal». (Рисунок 60)
            </TextPage>
            <BigPicture>
              <img src={image172} alt="image205" />
              <AltPicture>Рисунок 60 - Форма настройки состава ПГ для передачи в MODES-Terminal </AltPicture>
            </BigPicture>
            <TextPage>
              Для настройки типа параметра в MODES необходимо в поле «Значение MODES» ввести тип характеристики.
              <br />
              Для определения типа планового графика, в составе которого будет характеристика, необходимо проставить опции у требуемого типа ПГ.
            </TextPage>
            <BigPicture>
              <img src={image173} alt="image206" />
            </BigPicture>
            <TextPage>
              Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image18} alt="save" /> , для сброса нажать кнопку <img src={image58} alt="cancel" />{" "}
              .
            </TextPage>
          </Page>
          <Page>
            <DescriptionPage name="distribution_of_settings_in_DC">5.3. Распространение настроек в ДЦ</DescriptionPage>
            <TextPage>
              Для распространения настроек в ДЦ (Номер ПБР [1СЗ], Номер ПБР [2СЗ], Характеристики, Отсутствие данных, MODES-Terminal) необходимо нажать на кнопку
              <img src={image175} alt="protocol" /> .
              <br />
              Результаты распространения настроек можно посмотреть в протоколе по факту завершения распространения настроек, нажав кнопку{" "}
              <img src={image174} alt="protocol" /> .
              <br />
              Протокол распространения настроек имеет 4 состояния, под каждое состояние определена соответствующая цветовая палитра индикатора статуса протокола.
              <br />
              Статусы протокола:
              <br />
              <IconContainer>
                <Status type="gray" />
                <>- распространение настроек не выполнялось;</>
              </IconContainer>
              <br />
              <IconContainer>
                <Status type="yellow" />
                <> - распространение настроек успешно выполнено не во все ДЦ;</>
              </IconContainer>
              <br />
              <IconContainer>
                <Status type="green" />
                <> - распространение настроек успешно выполнено во все ДЦ;</>
              </IconContainer>
              <br />
              <IconContainer>
                <Status type="red" />
                <>- распространение настроек выполнено не успешно во все ДЦ.</>
              </IconContainer>
              <br />
              <IconContainer>
                <>Для индикатора</>
                <Status type="yellow" />
                <> при нажатии на протокол выводится список ДЦ, в которые распространение настроек выполнилось не успешно.</>
              </IconContainer>
            </TextPage>
            <BigPicture>
              <img src={image176} alt="image207" />
            </BigPicture>
          </Page>
          <Page>
            <DescriptionPage name="logging">6. ЖУРНАЛИРОВАНИЕ</DescriptionPage>
            <TextPage>
              Данный раздел Системы предназначен для журналирования действий пользователей, а также взаимодействия с внешними системами в Системе СРПГ СК-11.
              <br />
              Для перехода к функционалу Журналирования необходимо перейти в раздел <img src={image177} alt="image208" /> на главной рабочей панели.
            </TextPage>
          </Page>
          <Page>
            <TitlePage> Журнал взаимодействия с внешними системами</TitlePage>
            <TextPage>
              Для перехода к просмотру журнала взаимодействия с внешними системами необходимо перейти на вкладку <img src={image178} alt="image209" /> .
              <br />
              Пример формы:
            </TextPage>
            <BigPicture>
              <img src={image179} alt="image208" />
            </BigPicture>
            <TextPage>
              Для просмотра журналов действия пользователей за выбранную дату или период, необходимо нажать на поле даты, затем выбрать на месяце день или период для
              просмотра и нажать кнопку <img src={image180} alt="choose" /> .
            </TextPage>
            <BigPicture>
              <img src={image181} alt="image209" />
            </BigPicture>
            <TextPage>
              На форме доступны следующие инструменты:
              <br />
              <IconContainer>
                <div>
                  <Icon width={16} name="search" />
                </div>
                - Выполнить фильтр по полю.
              </IconContainer>
              <img src={image74} alt="image43" />- Выполнить сортировку по полю.
              <br />
              <img src={image182} alt="excel" />- Выполнить экспорт журнала в файл excel.
            </TextPage>
          </Page>
          <Page>
            <TitlePage>Журнал действий пользователей</TitlePage>
            <TextPage>
              Для перехода к просмотру журнала взаимодействия с внешними системами необходимо перейти на вкладку <img src={image183} alt="image210" /> .
              <br />
              Пример формы:
            </TextPage>
            <BigPicture>
              <img src={image184} alt="image211" />
            </BigPicture>
            <TextPage>
              Для просмотра журналов действия пользователей за выбранную дату или период, необходимо нажать на поле даты, затем выбрать на месяце день или период для
              просмотра и нажать кнопку <img src={image180} alt="choose" /> .
            </TextPage>
            <BigPicture>
              <img src={image185} alt="image212" />
            </BigPicture>
            <TextPage>
              На форме доступны следующие инструменты:
              <br />
              <IconContainer>
                <div>
                  <Icon width={16} name="search" />
                </div>
                - Выполнить фильтр по полю.
              </IconContainer>
              <img src={image74} alt="image43" />- Выполнить сортировку по полю.
              <br />
              <img src={image182} alt="excel" />- Выполнить экспорт журнала в файл excel.
            </TextPage>
          </Page>
        </RightContent>
      </Content>
    </Container>
  );
};
