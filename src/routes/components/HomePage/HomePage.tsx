import React, { useEffect } from "react";
import { observer } from "mobx-react";
import { updateToken } from "../../../utils/axios";
import { useNavigate } from "react-router-dom";
import { getRefreshToken } from "../../../utils/localStorage";

export const HomePage = observer(() => {
  const refreshToken = getRefreshToken();
  const isValidRefresh = refreshToken !== null && refreshToken !== "undefined" && refreshToken !== "null";
  const history = useNavigate();
  const replaceList = async () => {
    return await updateToken().then(() => {
      const pathname = localStorage.getItem(`pathname`) && localStorage.getItem(`pathname`) !== '/' ? localStorage.getItem(`pathname`) : null;
      if(pathname){
        history(pathname);
      }else {
        let res = "";
        const roles = JSON.parse(localStorage.getItem("roles") as string) || [];
        const [role] = roles;
        if (role === "sys_admin") {
          res = `/settings`;
        }
        if (role === "nsi_admin") {
          res = `/nci`;
        }
        if (role === "engineer") {
          res = `/planned-schedules`;
        }
        if (role === "viewer") {
          res = `/nci`;
        }
        if (role === "nsi_ess_admin") {
          res = `/nci`;
        }
        // res = `/login`;
        history(res);
      }
    });
  };

  useEffect(() => {
    if (isValidRefresh) {
      replaceList();
    } else {
      history("/login");
    }
  }, []);

  return <div></div>;
});
