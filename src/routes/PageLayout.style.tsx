import styled, { css } from "styled-components";
import { Icon } from "components/Icon";
import { Button } from "../components/Button";
import { ButtonsGroup } from "components/ButtonsGroup";
import { motion } from "framer-motion";

export const SearchPage = styled(Button)`
  width: 24px;
  height: 24px;
  min-width: 24px;
  min-height: 24px;
  border-radius: 50%;
  // color: ${(p) => p.theme.colorGuide};
  color: black;
`;

export const NotificationContainer = styled.div`
  margin-top: 4px;
  color: ${(p) => p.theme.folderColor};
  cursor: pointer;
  transform: scale(1);
  transition: all 0.3s;
  &:hover {
    transform: scale(1.2);
  }
`;
export const Help = styled.div`
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 3px;
  background: transparent;
  cursor: pointer;
  transition: all 0.2s;
  user-select: none;
  color: ${(p) => p.theme.colorGuide};
  border-radius: 50%;
  margin-right: 4px;

  &:hover {
    background-color: ${(props) => props.theme.lightGray};
    border-color: ${(props) => props.theme.primaryColorHover};
  }

  &:active {
    color: ${(props) => props.theme.buttonSecondaryColorActive};
    border-color: ${(props) => props.theme.buttonSecondaryColorActive};
  }
`;

export const ButtonsGroupStyled = styled(ButtonsGroup)`
  margin-left: auto;
  margin-right: 10px;
`;

export const ModeContainer = styled.div`
  margin-left: auto;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const VersionContainer = styled.div`
  position: absolute;
  left: auto;
  top: 16px;
  font-size: 10px;
  right: 62px;
  user-select: none;
  color: ${(p) => p.theme.topMenuTextColor};
`;

export const Container = styled.div`
  width: 100%;
  height: 100%; //100vh
  display: flex;
  flex-direction: row;
  color: ${(p) => p.theme.textColor};
`;

export const LeftContainer = styled.div`
  height: 100%;
  width: 72px;
  background-color: ${(p) => p.theme.white};
`;

export const RightContainer = styled.div`
  width: 100%;
  height: 100%;
  background-color: ${(p) => p.theme.backgroundColor};
  display: flex;
  flex-direction: column;
  overflow: hidden;
`;

export const TopMenu = styled.div`
  position: relative;
  width: 100%;
  height: 28px; //40px
  min-height: 28px; //20px
  max-height: 28px;
  background-color: ${(p) => p.theme.backgroundMenuColor};

  display: flex;
  align-items: center;
`;

export const UserContainer = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  //margin-left: auto;
  margin-right: 10px;
`;

export const TitleUserContainer = styled.h4`
  cursor: default;
  margin-left: 10px;
  color: ${(p) => p.theme.textColor};
`;

export const UserIcon = styled.div`
  margin-left: 10px;
`;

export const MenuIconArrow = styled.div<{ isOpen?: boolean }>`
  color: ${(p) => p.theme.gray};
  transform: rotate(90deg);
  height: 100%;
  margin-left: 5px;

  ${(p) =>
    p.isOpen &&
    css`
      transform: rotate(270deg);
    `}
`;

export const ActionUser = styled.div`
  cursor: pointer;
  display: flex;
  align-items: center;
  position: relative;
`;

export const IconAction = styled.div<{ isDarkTheme?: boolean }>`
  width: 24px;
  height: 24px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: ${(p) => p.theme.gray};
  margin-left: 10px;
  transition: all 0.3s;

  &:hover {
    background-color: ${(p) => p.theme.primaryColor};
    color: ${(p) => p.theme.white};
  }

  ${(p) =>
    p.isDarkTheme &&
    css`
      color: ${(p) => p.theme.backgroundColorSecondary};
    `}
`;

export const ChildrenContainer = styled.div`
  height: 100%;
  margin: 0;
`;

export const IconLogo = styled(Icon).attrs(() => ({ name: "logo", width: 16 }))`
  margin-left: 10px;
  cursor: pointer;
`;

export const IconLogoWhite = styled(Icon).attrs(() => ({ name: "logoWhite", width: 16 }))`
  margin-left: 10px;
  cursor: pointer;
`;

export const DetailUser = styled.div`
  position: absolute;
  min-height: 300px;
  max-height: 350px;
  box-shadow: rgb(0 0 0 / 28%) 0px 2px 4px;
  right: 10px;
  top: 24px; //45
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  border-radius: 7px;
  z-index: 1000;
  width: 240px;
  padding: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
`;

export const RowTitle = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const DetailUserTitle = styled.div<{ isVersion?: boolean; isRole?: boolean; width?: any }>`
  cursor: default;
  user-select: none;
  margin: 2px 0;
  color: ${(p) => p.theme.textColor};
  font-weight: bold;
  //width: 160px;
  //display: flex;
  //align-items: center;
  //justify-content: center;
  white-space: nowrap; /* Запрещаем перенос строк */
  overflow: hidden; /* Обрезаем все, что не помещается в область */
  text-overflow: ellipsis; /* Добавляем многоточие */
  ${(p) =>
    p.isVersion &&
    css`
      font-size: 10px;
    `}
  ${(p) =>
    p.isRole &&
    css`
      font-weight: normal;
    `}
  ${(p) =>
    p.width &&
    css`
      width: ${p.width}px;
    `}
`;

export const ButtonStyle = styled(Button)`
  width: 200px;
  height: 28px;
  margin: 2px 0;
`;
