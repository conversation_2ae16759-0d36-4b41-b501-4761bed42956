import React, { FC, lazy, useEffect, useRef, useState } from "react";
import {
  Container,
  LeftContainer,
  RightContainer,
  TopMenu,
  UserContainer,
  TitleUserContainer,
  UserIcon,
  MenuIconArrow,
  ActionUser,
  IconAction,
  ChildrenContainer,
  IconLogo,
  DetailUser,
  DetailUserTitle,
  ButtonStyle,
  IconLogoWhite,
  VersionContainer,
  ModeContainer,
  ButtonsGroupStyled,
  Help,
  NotificationContainer,
  SearchPage,
  RowTitle,
} from "./PageLayout.style";
import { useNavigate, useLocation } from "react-router-dom";
const LiveTimer = lazy(() => import("components/LiveTimer"));
import { Tabs } from "components/Tabs";
import { useOnClickOutside } from "../hooks/useOnClickOutside";
import { Icon } from "components/Icon";
import { tree } from "./tree";
import { useStores } from "../stores/useStore";
import { ModalSettings } from "./components/ModalSettings";
import { Avatar } from "components/Avatar";
// @ts-ignore
import packageJson from "../../package.json";
import { observer } from "mobx-react";
import { isModeCenter } from "utils/getMode";
import { DEFAULT_DAY, DEFAULT_MONTH, DEFAULT_YEAR } from "helpers/DateUtils";
import queryString from "query-string";
import { typesSRPG } from "pages/Nsi/Nsi";
import { deleteDB } from "../utils/indexDB";
import { clearStorage, getToken } from "../utils/localStorage";
import { checkTokenTime } from "../utils/axios";
import { Roles } from "../helpers/Roles";

export const PageLayout: FC<{
  children?: JSX.Element;
  selectedTheme?: any;
  setSelectedTheme?: any;
  themeEnable: boolean;
}> = observer(({ children, selectedTheme, setSelectedTheme, themeEnable }) => {
  const history = useNavigate();
  const location = useLocation();
  const { day = DEFAULT_DAY, month = DEFAULT_MONTH, year = DEFAULT_YEAR, selectedSegment, srpg }: any = queryString.parse(location.search);
  const { authStore, liveTimerStore, notificationStore, plannedSchedulesStore } = useStores();
  const { userInfo, isCenter: isAuthCenter } = authStore;

  useEffect(() => {
    const token = localStorage.getItem("token") ?? null;
    const login = localStorage.getItem("login") ?? null;
    if (token) {
      authStore.getUserInfo(login);
    }
  }, []);

  const [visibilityState, setVisibilityState] = useState(true);

  window.addEventListener("focus", () => {
    setVisibilityState(true);
  });
  window.addEventListener("blur", () => {
    setVisibilityState(false);
  });

  useEffect(() => {
    if (visibilityState) {
      authStore.getNotifications(isModeCenter);
      const notificationsArray = notificationStore.toastList ?? [];
      let notifications: any = [];
      if (notificationsArray?.length > 10) {
        notifications = notificationsArray.slice(0, 10);
      } else {
        notifications = notificationsArray;
      }
      if (notifications && notifications?.length > 0) {
        notifications.map((el: any) => {
          notificationStore.addNotification(el);
        });
      }
    }
    return () => {
      authStore.stopNotifications();
    };
  }, [visibilityState]);

  const userName = localStorage.getItem("login") ?? "-";

  const initIsCenter = JSON.parse(localStorage.getItem("isCenter") as string) ?? true;
  const [isCenter, setIsCenter] = useState(initIsCenter);

  useEffect(() => {
    if (!isHelp) {
      authStore.saveCenterMode(isCenter);
    }
  }, [isCenter]);

  const isCenterStand = isCenter && isModeCenter;

  // Отслеживает смену части (Централизованная/Распределенная) и перенаправляет пользователя,
  // если он оказался на странице "Уведомления" в Централизованной части.
  useEffect(() => {
    if (isCenterStand && location.pathname === "/notifications") {
      history("/settings"); // Перенаправляем в раздел "Настройки"
    }
  }, [isCenterStand, location.pathname, history]);

  const tabs = tree
    .map((item) => ({
      value: item.link,
      label: item.name,
      rules: item.rules,
    }))
    .filter(({ rules }) => {
      return rules.some((item) => {
        return userInfo.roles.some((el: string) => el === item);
      });
    })
    .filter((item) => {
      if (item.value === "/") {
        return false;
      }
      if (isCenterStand && item.value === "/notifications") {
        return false;
      }
      return true;
    });

  const [isOpenDetail, setIsOpenDetail] = useState(false);

  const refDetailUser = useRef<HTMLDivElement>(null);

  useOnClickOutside(refDetailUser, () => setIsOpenDetail(false));

  const [isOpenModalSettings, setIsOpenModalSettings] = useState(false);

  // eslint-disable-next-line react/prop-types
  const isSRDK = selectedTheme.split("_")[0] === "SRDK" ?? false;

  const initIsShowVersion = localStorage.getItem("isShowVersion") ?? true;
  const [isShowVersion, setIsShowVersion] = useState(initIsShowVersion);

  const initNameAvatars = (localStorage.getItem("avatar") as string) ?? "default";
  const [nameAvatars, setNameAvatars] = useState(initNameAvatars);

  const initComboboxStyle = (localStorage.getItem("comboboxStyle") as string) ?? "default";
  const [comboboxStyle, setComboboxStyle] = useState(initComboboxStyle);

  // Вычисляем доступность настройки "Отображать квитирование ПГ"
  // Только для пользователей с ролью "Технолог" в Централизованной части (ИА, ОДУ Востока)
  const isPgAckSettingAvailable = userInfo.roles.includes(Roles.engineer) && isCenter && isModeCenter;

  const isHelp = location.pathname === "/help";

  useEffect(() => {
    if (!isHelp) {
      authStore.saveCombobox(comboboxStyle);
    }
  }, [comboboxStyle]);

  const getHelpUrl = () => {
    if (location.pathname === "/nci") {
      if (isCenterStand) {
        return "/help#nsi";
      } else {
        return "/help#work_nsi";
      }
    }
    if (location.pathname === "/notifications") {
      return "/help#event_notifications";
    }
    if (location.pathname === "/planned-schedules") {
      if (isCenterStand) {
        return "/help#scheduled_schedules";
      } else {
        return "/help#working_with_scheduled_schedules";
      }
    }
    if (location.pathname === "/administration") {
      return "/help#administration";
    }
    if (location.pathname === "/settings") {
      return "/help#system_settings";
    }
    if (location.pathname === "/journaling") {
      return "/help#logging";
    }
    return "/help";
  };

  useEffect(() => {
    liveTimerStore.getTimeServer();
  }, [location.pathname, location.search]);

  useEffect(() => {
    authStore.changeTheme(selectedTheme);
  }, [selectedTheme]);

  const getStand = () => {
    if (process.env.STAND === "main") {
      if (process.env.MODE === "center") {
        return `ЦДУ`;
      } else {
        return `ДЦ [1СЗ]`;
      }
    } else {
      if (process.env.MODE === "center") {
        return `ОДУ Востока`;
      } else {
        return `ДЦ [2СЗ]`;
      }
    }
  };

  const handleStorageChange = () => {
    const login = localStorage.getItem("login");
    const token = getToken();
    const isValidToken = checkTokenTime(getToken()) && token !== "undefined" && token !== "null";
    const isValidTokenLS = token && isValidToken;
    if (window.location.pathname !== "/login" && !isValidTokenLS) {
      window.location.reload();
    }
    if (authStore.userInfo.login !== login && authStore.userInfo.login !== "" && login !== "" && authStore.userInfo.login && login) {
      window.location.reload();
    }
  };

  useEffect(() => {
    window.addEventListener("storage", handleStorageChange);
    handleStorageChange();
    return () => {
      window.removeEventListener("storage", handleStorageChange);
    };
  }, []);

  return (
    <Container>
      <RightContainer>
        <TopMenu>
          {isSRDK ? (
            <IconLogoWhite
              onClick={() => {
                let res: any = null;
                const role = authStore.userInfo.roles[0];
                if (role === "sys_admin") {
                  res = `/settings`;
                }
                if (role === "nsi_admin") {
                  res = `/nci`;
                }
                if (role === "engineer") {
                  res = `/planned-schedules`;
                }
                if (role === "viewer") {
                  res = `/nci`;
                }
                if (role === "nsi_ess_admin") {
                  res = `/nci`;
                }
                history(res);
                localStorage.setItem(`pathname`, res);
              }}
            />
          ) : (
            <IconLogo
              onClick={() => {
                let res: any = null;
                const role = authStore.userInfo.roles[0];
                if (role === "sys_admin") {
                  res = `/settings`;
                }
                if (role === "nsi_admin") {
                  res = `/nci`;
                }
                if (role === "engineer") {
                  res = `/planned-schedules`;
                }
                if (role === "viewer") {
                  res = `/nci`;
                }
                if (role === "nsi_ess_admin") {
                  res = `/nci`;
                }
                history(res);
                localStorage.setItem(`pathname`, res);
              }}
            />
          )}
          <Tabs
            tabs={tabs}
            isUserSelected
            onClick={({ value }: { value: any }) => {
              history(value);
              localStorage.removeItem(`tableOpened-${1}`);
              localStorage.removeItem(`tableOpened-${2}`);
              localStorage.removeItem(`tableOpened-${3}`);
              localStorage.removeItem(`tableOpened-${4}`);
              localStorage.removeItem(`tableOpened-${5}`);
              localStorage.removeItem(`tableOpened-${6}`);
              localStorage.removeItem(`tableOpened-${7}`);
              localStorage.removeItem(`tableOpened-${8}`);
              localStorage.removeItem("table-checked-items-5");
              if (value !== "/planned-schedules") {
                plannedSchedulesStore.resetTable();
                plannedSchedulesStore.stopDistribution();
                plannedSchedulesStore.stopView();
                plannedSchedulesStore.stopStatusPlanned();
              }
              localStorage.setItem(`pathname`, value);
            }}
            selectedValue={location.pathname}
            isTopMenu={isSRDK}
          />
          <ModeContainer>
            <Help
              onClick={() => {
                window.open(getHelpUrl());
              }}
              title="Справка"
            >
              <Icon width={24} name="help" />
            </Help>
            {isModeCenter && (
              <ButtonsGroupStyled
                items={[
                  { value: true, label: "Централизованная" },
                  {
                    value: false,
                    label: "Распределенная",
                    dataTest: "header.distributed-button",
                  },
                ]}
                selectedValue={isCenter}
                onClick={(value) => {
                  if (location.pathname === "/planned-schedules") {
                    plannedSchedulesStore.resetViewTable();
                  }
                  setIsCenter(value);
                  localStorage.setItem("isCenter", value);
                  if (location.pathname === "/nci") {
                    history(
                      `?year=${year ?? DEFAULT_YEAR}&month=${month ?? DEFAULT_MONTH}&day=${day ?? DEFAULT_DAY}&selectedSegment=${selectedSegment ?? "goy"}&srpg=${
                        srpg ?? typesSRPG[0].value
                      }&viewPage=view`
                    );
                  }
                }}
                isNavBar
              />
            )}
          </ModeContainer>
          <UserContainer>
            <LiveTimer viewStatus isSRDK={isSRDK} />
            <ActionUser onMouseDown={(e: React.MouseEvent<HTMLDivElement>) => e.stopPropagation()} onClick={() => setIsOpenDetail((prev) => !prev)}>
              <UserIcon>{nameAvatars === "default" ? <Icon name="user" width={16} /> : <Avatar size={16} name={nameAvatars} />}</UserIcon>
              <MenuIconArrow isOpen={isOpenDetail}>
                <Icon name="menuArrow" width={6} />
              </MenuIconArrow>
            </ActionUser>

            {isOpenDetail && (
              <DetailUser ref={refDetailUser}>
                {nameAvatars === "default" ? <Icon name="user" width={100} /> : <Avatar size={100} name={nameAvatars} />}
                {isShowVersion && (
                  <DetailUserTitle isVersion={true}>
                    {/*Версия {packageJson.version} {process.env.MODE === "center" ? "Центр" : "ДЦ"} {process.env.STAND === "main" ? "ЦДУ" : "ОЗ Восток"}*/}
                    Версия {packageJson.version}HF2 {getStand()}
                  </DetailUserTitle>
                )}
                <DetailUserTitle isVersion={true}>{userInfo?.department ?? ""}</DetailUserTitle>
                <DetailUserTitle title={`Логин : ${userName}`}>{userName}</DetailUserTitle>
                <DetailUserTitle title={`Имя : ${userInfo.fio}`} width={userInfo.fio.length > 20 ? 140 : null}>
                  {/*width={140}*/}
                  {userInfo.fio}
                </DetailUserTitle>
                {userInfo?.rolesRus &&
                  userInfo?.rolesRus.map((el: any, index: number) => {
                    return (
                      <DetailUserTitle title={`Роль : ${el}`} isRole={true} key={`role-rus-${index}`}>
                        {el}
                      </DetailUserTitle>
                    );
                  })}
                <ButtonStyle
                  title="Настройки"
                  onClick={() => {
                    setIsOpenDetail(false);
                    setIsOpenModalSettings(true);
                  }}
                  icon="settings"
                  widthIcon={18}
                  type="secondary"
                />
                <ButtonStyle
                  title="Выход из системы"
                  onClick={async () => {
                    const pathname = `${location.pathname}${location.search}`;
                    const login = authStore.userInfo.login ?? null;
                    const theme = localStorage.getItem("theme") ?? "SRPG_DEFAULT";
                    const isCenter = JSON.parse(localStorage.getItem("isCenter") as string) ?? true;
                    await authStore
                      .logout()
                      .then(() => {
                        const tokenHistory = localStorage.getItem("token_history");

                        localStorage.clear();
                        deleteDB();

                        if (tokenHistory) {
                          localStorage.setItem("token_history", tokenHistory);
                        }
                      })
                      .then(() => {
                        if (pathname) {
                          localStorage.setItem("pathname", pathname);
                        }
                        if (login) {
                          localStorage.setItem("login", login);
                        }
                        localStorage.setItem("theme", theme);
                        localStorage.setItem("isCenter", isCenter);
                        window.location.replace("/login");
                      });
                  }}
                  icon="logout"
                  widthIcon={22}
                />
              </DetailUser>
            )}
          </UserContainer>
        </TopMenu>
        <ChildrenContainer>{children}</ChildrenContainer>
      </RightContainer>
      {isOpenModalSettings && (
        <ModalSettings
          onCancel={() => setIsOpenModalSettings(false)}
          selectedTheme={selectedTheme}
          setSelectedTheme={setSelectedTheme}
          isShowVersion={isShowVersion}
          setIsShowVersion={setIsShowVersion}
          nameAvatars={nameAvatars}
          setNameAvatars={setNameAvatars}
          comboboxStyle={comboboxStyle}
          setComboboxStyle={setComboboxStyle}
          themeEnable={themeEnable}
          isPgAckSettingAvailable={isPgAckSettingAvailable}
        />
      )}
    </Container>
  );
});
