import axios, { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse } from "axios";
import { getToken, getRefreshToken, setTokens, clearStorage, getTokenReceiptTime } from "./localStorage";
import { stores } from "stores/RootStore";
import { ServerError, NotFoundError, ResponseError, ForbiddenError } from "./Error";
import { deleteDB } from "./indexDB";
import { logTokenEvent, maskToken } from "./tokenLogger";

// Удален вывод в консоль в связи с обращением СИБов (SRPG-2840)

let isAuthentication: any = null;
let isUpdatingToken = false; // Защита от множественных вызовов

const createMainAxiosInstance = () => {
  return axios.create({
    headers: {
      "Content-Type": "application/json",
    },
    timeout: 900000,
  });
};

export const checkTokenTime = (token: any) => {
  if (token) {
    const tokenUpdateTime = getTokenReceiptTime();
    const currentTime = Math.floor(Date.now() / 1000);
    const timeDifference = currentTime - tokenUpdateTime;
    return timeDifference < 300; // если валидный  (токен обновляем раз в 50 секунд)  300
  }
  return false; // если не валидный
};

export const updateToken = async () => {
  // Защита от множественных одновременных вызовов
  if (isUpdatingToken) {
    return;
  }

  const update = async () => {
    try {
      isUpdatingToken = true;
      // Получаем актуальный refreshToken непосредственно перед отправкой запроса
      const refreshToken = getRefreshToken();
      // ЛОГИРОВАНИЕ ПРИ НАЧАЛЕ ОБНОВЛЕНИЯ
      logTokenEvent("REFRESH_INITIATED", {
        refreshToken: maskToken(refreshToken),
      });
      console.warn("%c REFRESH-TOKEN/INITIATED ", "background: #1890ff; color: white; font-weight: bold; padding: 2px 6px; border-radius: 4px;", { refreshToken });
      if (refreshToken === "undefined" || refreshToken === "null" || !refreshToken) {
        // ЛОГИРОВАНИЕ ПРИ ОТСУТСТВИИ REFRESH-TOKEN
        console.warn("%c REFRESH-TOKEN/ABORTED ", "background: #1890ff; color: white; font-weight: bold; padding: 2px 6px; border-radius: 4px;", { refreshToken });
        logTokenEvent("REFRESH_ABORTED", {
          reason: "Отсутствует refresh token в localstorage",
        });
        window.location.replace("/login");
        return;
      }

      const data = await fetch("/srpg-control/api/v1/auth/refresh-token", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ refreshToken }),
      });

      if (data.status === 500) {
        const refreshToken = getRefreshToken();
        const token = getToken();
        // ЛОГИРОВАНИЕ ПРИ 500 ОШИБКЕ
        console.warn("%c REFRESH-TOKEN/500 ", "background: #1890ff; color: white; font-weight: bold; padding: 2px 6px; border-radius: 4px;", { refreshToken });
        logTokenEvent("REFRESH_FAILED/500", {
          status: data.status,
          usedRefreshToken: maskToken(refreshToken),
        });
        if (token && refreshToken) {
          return;
        } else {
          // clearStorage();
          window.location.replace("/login");
          // localStorage.removeItem("isCenter");
          // deleteDB();
          // const pathname = JSON.parse(localStorage.getItem("pathname") as string) ?? null;
          // const theme = JSON.parse(localStorage.getItem("theme") as string) ?? null;
          // localStorage.clear();
          // if (pathname) {
          //   localStorage.setItem("pathname", pathname);
          //   localStorage.setItem("theme", theme);
          // }
          // const pathname = localStorage.getItem("pathname") ?? null;
          const pathname = `${location.pathname}${location.search}`;
          const login = localStorage.getItem("login") ?? null;
          const theme = localStorage.getItem("theme") ?? "SRPG_DEFAULT";
          const tokenHistory = localStorage.getItem("token_history");
          localStorage.clear();
          if (tokenHistory) {
            localStorage.setItem("token_history", tokenHistory);
          }
          deleteDB();
          if (pathname) {
            localStorage.setItem("pathname", pathname);
          }
          if (login) {
            localStorage.setItem("login", login);
          }
          localStorage.setItem("theme", theme);
        }
      }
      if (data.status === 401 || data.status === 403) {
        // clearStorage();
        // ЛОГИРОВАНИЕ ПРИ 401/403 ОШИБКЕ
        console.warn("%c REFRESH-TOKEN/401_403 ", "background: #1890ff; color: white; font-weight: bold; padding: 2px 6px; border-radius: 4px;", { refreshToken });
        logTokenEvent("REFRESH_FAILED/401_403", {
          status: data.status,
          usedRefreshToken: maskToken(refreshToken),
        });
        localStorage.removeItem("token");
        localStorage.removeItem("refreshToken");
        window.location.replace("/login");
        // localStorage.removeItem("isCenter");
        // deleteDB();
        // const pathname = JSON.parse(localStorage.getItem("pathname") as string) ?? null;
        // localStorage.clear();
        // if (pathname) {
        //   localStorage.setItem("pathname", pathname);
        // }
        const pathname = localStorage.getItem("pathname") ?? null;
        const login = localStorage.getItem("login") ?? null;
        const theme = localStorage.getItem("theme") ?? "SRPG_DEFAULT";
        const isCenter = JSON.parse(localStorage.getItem("isCenter") as string) ?? true;
        deleteDB();
        if (pathname) {
          localStorage.setItem("pathname", pathname);
        }
        if (login) {
          localStorage.setItem("login", login);
        }
        localStorage.setItem("theme", theme);
        localStorage.setItem("isCenter", isCenter);
      } else {
        const response = await data.json();
        setTokens({ token: response.token, refreshToken: response.refreshToken });
        // ЛОГИРОВАНИЕ ПРИ УСПЕШНОМ ОБНОВЛЕНИИ
        console.warn("%c REFRESH-TOKEN/SUCCESS ", "background: #1890ff; color: white; font-weight: bold; padding: 2px 6px; border-radius: 4px;", { refreshToken });
        logTokenEvent("REFRESH_SUCCESS", {
          newRefreshToken: maskToken(response.refreshToken),
        });
        //maybe
        // stores.authStore.getUserInfo()
        //maybe
      }
    } catch (e) {
      // ЛОГИРОВАНИЕ ПРИ ОШИБКЕ
      console.warn("%c REFRESH-TOKEN/EXCEPTION_CATCHED ", "background: #1890ff; color: white; font-weight: bold; padding: 2px 6px; border-radius: 4px;", e);
      logTokenEvent("REFRESH_EXCEPTION", { error: e?.message || e?.toString() || JSON.stringify(e) || "Неизвестная ошибка" });
      const pathname = `${location.pathname}${location.search}`;
      const login = localStorage.getItem("login") ?? null;
      const theme = localStorage.getItem("theme") ?? "SRPG_DEFAULT";
      clearStorage();
      deleteDB();
      if (pathname && pathname !== "/login") {
        localStorage.setItem("pathname", pathname);
      }
      if (login) {
        localStorage.setItem("login", login);
      }
      localStorage.setItem("theme", theme);
      window.location.replace("/login");
    } finally {
      isUpdatingToken = false;
    }
  };
  if (!isAuthentication) isAuthentication = update();
  await isAuthentication;
  isAuthentication = null;
};

// Interceptor для каждого запроса, который будет делать refresh токена, если он истек
const createRequestInterceptorAuth = (instance: AxiosInstance) => {
  instance.interceptors.request.use(
    async (config: any) => {
      let token;
      if (isAuthentication) {
        await isAuthentication;
      }
      token = getToken();
      const refreshToken = getRefreshToken();
      const isValidRefresh = refreshToken !== null && refreshToken !== "undefined" && refreshToken !== "null";
      const isValidToken = checkTokenTime(getToken());

      if (token && !isValidToken && isValidRefresh && !isUpdatingToken) {
        await updateToken();
      }
      token = getToken();
      if (token) {
        config.headers["Authorization"] = token;
      }
      return config;
    },
    (err: string) => Promise.reject(err)
  );
};

const createResponseInterceptorAuth = (instance: AxiosInstance) => {
  instance.interceptors.response.use(
    (response: AxiosResponse) => response.data,
    async (err: any) => {
      if (err.message === "request_canceled") {
        return Promise.reject(err.message);
      }

      // Защита от обработки запросов refresh-token
      const isRefreshTokenRequest = err?.config?.url?.includes("/auth/refresh-token");
      if (isRefreshTokenRequest) {
        return Promise.reject(err);
      }

      const refreshToken = getRefreshToken();
      const isValidRefresh = refreshToken !== null && refreshToken !== "undefined" && refreshToken !== "null";
      if (!isValidRefresh) {
        const pathname = `${location.pathname}${location.search}`;
        const login = localStorage.getItem("login") ?? null;
        const theme = localStorage.getItem("theme") ?? "SRPG_DEFAULT";
        clearStorage();
        if (pathname) {
          localStorage.setItem("pathname", pathname);
        }
        if (login) {
          localStorage.setItem("login", login);
        }
        localStorage.setItem("theme", theme);
        window.location.replace("/login");
      }
      const isLogin = location.pathname === "/login";
      if (!isLogin) {
        if (err?.response?.status === 401 || err?.response?.status === 403) {
          // Добавлена проверка флага и повторение запроса
          if (!isUpdatingToken) {
            try {
              await updateToken();
              // Повторяем оригинальный запрос с обновленным токеном
              const newToken = getToken();
              if (newToken) {
                err.config.headers["Authorization"] = newToken;
                return instance.request(err.config);
              }
            } catch (refreshError) {
              // Если обновление токена не удалось, продолжаем обработку ошибки
            }
          }
        }
      }

      //maybe
      // if (err?.response?.status === 504 && window.location.pathname !== "/login") {
      //   clearStorage();
      //   window.location.replace("/login");
      // }
      // maybe

      const getErrorBlob = async () => {
        const jsonPromise =
          typeof err.response.data === "string" ? null : err?.response?.data.text ? await err?.response?.data?.text() : JSON.stringify({ error: "Неизвестная ошибка" });
        return jsonPromise ? JSON.parse(jsonPromise)?.error : "Сервис не доступен"; // "Для пользователя не назначена ни одна роль в Системе" "Неизвестная ошибка"
      };

      const errorDescription = err.response.data.error ? err.response.data.error : await getErrorBlob();

      // if (err.response.status === 403) {
      //   stores.notificationStore.addNotification({
      //     title: "Ошибка",
      //     description: errorDescription,
      //     icon: "error",
      //     type: "error",
      //   });
      //   mainCancelToken.cancel("request_canceled");
      // }
      // const isLogin = window.location.href === "http://localhost:3000/login";

      if (err.response.status === 504) {
        stores.notificationStore.addNotification({
          title: "504",
          description: "Время ожидания шлюза истекло",
          icon: "error",
          type: "error",
        });
        return Promise.reject();
      } else if (err.response.status === 404) {
        stores.notificationStore.addNotification({
          title: "Предупреждение",
          description: errorDescription,
          icon: "warning",
          type: "warning",
        });
        return Promise.reject();
      } else if (err.response.status === 423) {
        stores.notificationStore.addNotification({
          title: "Предупреждение",
          description: errorDescription,
          icon: "warning",
          type: "warning",
        });
        return Promise.reject();
      } else if (err.response.status === 400) {
        // const res = err?.response?.data?.error ? err?.response?.data?.error : await err?.response?.data?.text();
        // const { error } = JSON.parse(res);
        stores.notificationStore.addNotification({
          title: "Предупреждение",
          description: errorDescription, //err.response.data.error,
          icon: "warning",
          type: "warning",
        });
        return Promise.reject({ code: 400, message: "Предупреждение", data: errorDescription });
      } else if (err.response.status === 502) {
        if (isLogin) {
          stores.notificationStore.addNotification({
            title: "Ошибка",
            description: errorDescription,
            icon: "error",
            type: "error",
          });
        }
        return;
      } else if (err.response.status !== 429) {
        stores.notificationStore.addNotification({
          title: "Ошибка",
          description: errorDescription,
          icon: "error",
          type: "error",
        });
        return Promise.reject();
      }

      if (err.response.status === 429) {
        const RetryAfter = err.response.headers["retry-after"];
        const time = RetryAfter ? Number(RetryAfter) * 1000 : 10000;
        return Promise.reject({ code: 429, time });
      } else if (err.response.status === 404) {
        // return Promise.reject(new NotFoundError({ ...err.response.data, responseStatusCode: err.response.status }));
        // return Promise.reject({ code: 404 });
        return Promise.reject({ code: 404 });
      } else if (err.response.status === 502) {
        // if (isLogin) {
        //   return Promise.reject({ code: 502, message: "Сервис не доступен", data: "Сервис не доступен" });
        // }
        return Promise.reject({ code: 502, message: "Сервис не доступен", data: "Сервис не доступен" });
      } else if (err.response.status === 500) {
        return Promise.reject(new ServerError({ ...err.response.data, responseStatusCode: err.response.status }));
      } else if (err.response.status === 403) {
        return Promise.reject(new ForbiddenError({ ...err.response.data, responseStatusCode: err.response.status }));
      } else {
        return Promise.reject(new ResponseError({ ...err.response.data, responseStatusCode: err.response.status })); // Любая другая ошибка
      }
    }
  );
};
(window as any).updateToken = updateToken; // Добавляем функцию в глобальный объект для отладки в консоли браузера
const axiosInstance = createMainAxiosInstance();

createRequestInterceptorAuth(axiosInstance);
createResponseInterceptorAuth(axiosInstance);

export { axiosInstance };
