import { RootStore } from "stores/RootStore";
import { observable, action, makeAutoObservable, runInAction, toJS } from "mobx";
import { plannedSchedules } from "api/plannedSchedules";
import { DEFAULT_DAY, DEFAULT_MONTH, DEFAULT_YEAR, prepareDate, prepareDateTable } from "helpers/DateUtils";
import { delay } from "helpers/delay";
// @ts-ignore
import { saveAs } from "file-saver";
import { isEast, isModeCenter } from "utils/getMode";
import { getDepartments, sourceDepartments } from "../../api/nsi/nci-controller";
import { getToken } from "../../utils/localStorage";
import { timeApi } from "../../api/time";
import { SummaryStatusResponse, AckMessage } from "../../api/plannedSchedules/plannedSchedules-controller";

interface Status {
  status: string;
  startedAt?: string;
  finishedAt?: string;
  noData?: boolean;
}

export interface Department {
  depId: number;
  depName: string;
  depType: "CDU" | "ODU" | "RDU";
  controlId?: string;

  oik?: Status;
  modes?: Status;
  eg3?: Status;
  srdk?: Status;

  items?: Department[];
  childs?: Department[];

  tabId: string;
  name: string;
  isDisableChecked: boolean;
  oikValue: string;
  modesValue: string;
  srdkValue: string;
  parentId: string;
  rowColor?: "cdu" | "odu" | "rdu";
}

type ListDC = Department;

export type TableData = Department[];

// Удалены выводы в консоль в связи с обращением СИБов (SRPG-2840)
export class PlannedSchedulesStore {
  rootStore: RootStore;
  plannedSchedules: any[];
  listDC: ListDC[];
  statusPlanned: any[];
  pbrList: any[];
  viewTableData: any[];
  viewTableDataOriginal: any[];
  viewTableDataCenter: any[];
  viewTableDataCenterOriginal: any[];
  infoForSelected: object;
  isLoadingPlannedSchedules: boolean;
  isProcessing: boolean;
  isLoadingInfo: boolean;
  isLoadingXML: boolean;
  isLoadingXMLPackage: boolean;
  isLoadingListDc: boolean;
  isProcessingStatusPlanned: boolean;
  canMakeGlobalAccept: boolean;
  isStart: boolean;
  isProcessingTableView: boolean;
  isLoadingDetailInfo: boolean;
  isLoadingFile: boolean;
  isLoadingAccept: boolean;
  isAcceptSystem: boolean;
  isStatusRepeat: boolean;
  isStatusRepeatView: boolean;
  isLoadData: any;
  isRepeatAccept: boolean;
  isUplodaDataView: boolean;
  isLoadingDistribution: boolean;
  isLoadingBeforeAccept: boolean;
  isLoadingResetAccept: boolean;
  isLoadingRetry: any;
  selectedObjectView: any;
  lastTaskIdDistribution: any;
  timerId: any;
  objectLoaded: any;
  timerIdView: any;
  timerDistribution: any;
  canRepeatGlobalAccept: any;
  timerLeftMenu: any;
  loadDataTimer: any;
  xhr: any;
  isLoadingDetailEvents: any;
  infoDetailModalViewExternalSystem: any;
  eventsDistribution: any;
  totalXMLLoaded: number;
  alreadyAccepted: any;
  summaryStatus: SummaryStatusResponse | null;
  activeStatusModalPgId: string | null = null;
  private summaryStatusController: AbortController | null = null;
  ackMessages: AckMessage[];
  ackTimerId: ReturnType<typeof setTimeout> | null;
  constructor(rootStore: RootStore) {
    makeAutoObservable(this, {
      plannedSchedules: observable,
      listDC: observable,
      isLoadingPlannedSchedules: observable,
      viewTableData: observable,
      viewTableDataCenter: observable,
      viewTableDataOriginal: observable,
      viewTableDataCenterOriginal: observable,
      xhr: observable,
      loadDataTimer: observable,
      isLoadingListDc: observable,
      statusPlanned: observable,
      isLoadingXML: observable,
      alreadyAccepted: observable,
      isLoadingBeforeAccept: observable,
      pbrList: observable,
      isLoadingXMLPackage: observable,
      canMakeGlobalAccept: observable,
      isProcessingTableView: observable,
      isLoadingDetailInfo: observable,
      isProcessingStatusPlanned: observable,
      eventsDistribution: observable,
      canRepeatGlobalAccept: observable,
      isLoadData: observable,
      isLoadingRetry: observable,
      isLoadingFile: observable,
      isLoadingDistribution: observable,
      isStart: observable,
      isLoadingAccept: observable,
      isAcceptSystem: observable,
      isRepeatAccept: observable,
      selectedObjectView: observable,
      isLoadingResetAccept: observable,
      lastTaskIdDistribution: observable,
      isStatusRepeat: observable,
      isStatusRepeatView: observable,
      isLoadingDetailEvents: observable,
      isUplodaDataView: observable,
      objectLoaded: observable,
      timerId: observable,
      timerIdView: observable,
      timerLeftMenu: observable,
      infoDetailModalViewExternalSystem: observable,
      getXmlData: action,
      getStatusPlanned: action,
      initTableView: action,
      changeObjectView: action,
      initPlannedSchedules: action,
      stopStatusPlanned: action,
      changeLastTaskId: action,
      initDetailModalView: action,
      getTable: action,
      startRepeat: action,
      startDistributionForm: action,
      stopRepeat: action,
      saveUnSaveHours: action,
      sendPGBeforeAccept: action,
      resetAccept: action,
      initDetailDistribution: action,
      abortXml: action,
      checkAccept: action,
    });
    this.rootStore = rootStore;
    this.plannedSchedules = [];
    this.listDC = [];
    this.statusPlanned = isEast
      ? [
          { typeName: "ППБР", type: "DDG" },
          { typeName: "ПБР", type: "UDDG" },
          { typeName: "ПЭР", type: "PER" },
          { typeName: "ПДГ", type: "PDG" },
          { typeName: "Из файла", type: "XML" },
        ]
      : [
          { typeName: "ППБР", type: "PPBR" },
          { typeName: "ПБР", type: "PBR" },
          { typeName: "ПЭР", type: "PER" },
          { typeName: "ПДГ", type: "PDG" },
          { typeName: "Из файла", type: "XML" },
        ];
    this.viewTableData = [];
    this.viewTableDataOriginal = [];
    this.viewTableDataCenter = [];
    this.viewTableDataCenterOriginal = [];
    this.pbrList = [];
    this.eventsDistribution = [];
    this.infoForSelected = {};
    this.isLoadingPlannedSchedules = true;
    this.isLoadingListDc = false;
    this.isProcessing = false;
    this.isProcessingTableView = false;
    this.isProcessingStatusPlanned = false;
    this.canMakeGlobalAccept = false;
    this.isLoadingFile = false;
    this.isLoadingInfo = false;
    this.totalXMLLoaded = 0;
    this.isLoadingXML = false;
    this.objectLoaded = null;
    this.isLoadingXMLPackage = false;
    this.isStart = false;
    this.isLoadingDetailInfo = false;
    this.isLoadingAccept = false;
    this.isLoadingBeforeAccept = false;
    this.isLoadingResetAccept = false;
    this.isRepeatAccept = false;
    this.isAcceptSystem = false;
    this.isUplodaDataView = false;
    this.isLoadingDistribution = false;
    this.isStatusRepeat = false;
    this.isStatusRepeatView = false;
    this.isLoadingDetailEvents = true;
    this.isLoadData = null;
    this.canRepeatGlobalAccept = null;
    this.isLoadingRetry = null;
    this.selectedObjectView = null;
    this.lastTaskIdDistribution = null;
    this.timerDistribution = () => {};
    this.timerIdView = () => {};
    this.timerId = () => {};
    this.timerLeftMenu = () => {};
    this.loadDataTimer = () => {};
    this.infoDetailModalViewExternalSystem = { systemName: "", pgName: "", events: [] };
    this.xhr = null;
    this.alreadyAccepted = {};
    this.summaryStatus = null;
    this.ackMessages = [];
    this.ackTimerId = null;
  }

  getNotification(title: string, message: string, type: string, isKeepOnScreen: boolean) {
    const getType = () => {
      if (type === "INFO") {
        return "information";
      }
      if (type === "ERROR") {
        return "error";
      }
      if (type === "WARNING") {
        return "warning";
      }
    };

    const getIcon = () => {
      if (type === "INFO") {
        return "information";
      }
      if (type === "ERROR") {
        return "error";
      }
      if (type === "WARNING") {
        return "warning";
      }
    };

    this.rootStore.notificationStore.addNotification({
      title,
      description: message,
      icon: getIcon(),
      type: getType(),
      isTimer: !isKeepOnScreen,
    });
  }

  prepareAllListDc(array: any) {
    return array.map((item: any) => {
      let childs;
      if (item.childs && item.childs.length > 0) {
        childs = this.prepareAllListDc(item.childs);
      }
      if (item.items && item.items.length > 0) {
        childs = this.prepareAllListDc(item.items);
      }
      return {
        ...item,
        childs: childs,
        tabId: item.id || item.depId,
        name: (item.name || item.depName) ?? "-",
        isDisableChecked: !(item?.oik?.status === "ERROR_SENDING_TO_DC" || item?.oik?.status === "ERROR_SENDING_GLOBAL_ACCEPT"),
        oikValue: item?.oik?.status ?? "NO_DATA",
        modesValue: item?.modes?.status ?? "NO_DATA",
        srdkValue: item?.srdk?.status ?? "NO_DATA",
        // item?.oik?.status === "SENDING_TO_DC" ||
        // item?.oik?.status === "WAITING_FOR_ACCEPT" ||
        // item?.oik?.status === "WAITING_FOR_GLOBAL_ACCEPT" ||
        // item?.oik?.status === "SENDING_GLOBAL_ACCEPT" ||
        // item?.oik?.status === "SENDING_TO_OIK",
      };
    });
  }

  prepareAllListDcFlat(array: any) {
    let result: any = [];
    array.map((item: any) => {
      let childs;
      if (item.childs && item.childs.length > 0) {
        childs = this.prepareAllListDc(item.childs);
      }
      if (item.items && item.items.length > 0) {
        childs = this.prepareAllListDc(item.items);
      }
      result.push({
        ...item,
        tabId: item.id || item.depId,
        name: (item.name || item.depName) ?? "-",
        isDisableChecked:
          item?.oik?.status === "SENDING_TO_DC" ||
          item?.oik?.status === "WAITING_FOR_ACCEPT" ||
          item?.oik?.status === "WAITING_FOR_GLOBAL_ACCEPT" ||
          item?.oik?.status === "SENDING_GLOBAL_ACCEPT" ||
          item?.oik?.status === "SENDING_TO_OIK",
      });
      if (childs) {
        result = [...result, ...childs];
      }
    });
    return result;
  }

  checkStatusTableView(array: any) {
    return array.some(({ oik, modes, srdk, eg3, items }: { oik: any; modes: any; srdk: any; eg3: any; items: any[] }) => {
      let isCheckChilds = false;
      if (items && items.length > 0) {
        isCheckChilds = this.checkStatus(items);
      }
      return oik?.status === "CREATED" || modes?.status === "CREATED" || srdk?.status === "CREATED" || eg3?.status === "CREATED" || isCheckChilds;
    });
  }

  stopCheckStatusTableView() {
    this.isProcessingTableView = false;
  }

  checkStatusPlanned(array: any) {
    return array.some(({ status, items }: { status: any; items: any[] }) => {
      let isCheckChilds = false;
      if (items && items.length > 0) {
        isCheckChilds = this.checkStatus(items);
      }
      return status === "CREATED" || isCheckChilds;
    });
  }

  async loadStatusPlanned() {
    try {
      const { items: status } = await plannedSchedules.plannedSchedulesController.getStatus();
      runInAction(() => {
        const statusPlanned = isEast
          ? [
              { typeName: "ППБР", type: "DDG" },
              { typeName: "ПБР", type: "UDDG" },
              { typeName: "ПЭР", type: "PER" },
              { typeName: "ПДГ", type: "PDG" },
              { typeName: "Из файла", type: "XML" },
            ]
          : [
              { typeName: "ППБР", type: "PPBR" },
              { typeName: "ПБР", type: "PBR" },
              { typeName: "ПЭР", type: "PER" },
              { typeName: "ПДГ", type: "PDG" },
              { typeName: "Из файла", type: "XML" },
            ];
        runInAction(() => {
          this.statusPlanned = statusPlanned.map((item) => {
            const find = status?.filter(({ type }: { type: string }) => type === item.type)[0] ?? null;
            if (find) {
              return { ...item, ...find };
            }
            return item;
          });
        });
      });
    } catch (e) {}
  }

  async startCheckStatusPlanned() {
    try {
      const rolesAuth = this.rootStore.authStore.userInfo.roles;
      const isAccess = rolesAuth.some((el: string) => {
        return ["nsi_admin", "sys_admin", "engineer"].some((rule: any) => rule === el);
      });
      if (isAccess) {
        this.isProcessingStatusPlanned = true;
        while (this.isProcessingStatusPlanned) {
          await this.loadStatusPlanned();
          // await delay(1000);
          const processingTasks = this.checkStatusPlanned(this.statusPlanned);
          if (!processingTasks) {
            this.isProcessingStatusPlanned = false;
          }
        }
      }
    } catch (e) {
    } finally {
      this.isProcessingStatusPlanned = false;
    }
  }

  stopCheckStatusPlanned() {
    this.isProcessingStatusPlanned = false;
  }

  resetTable() {
    // this.viewTableDataCenter = [];
    // this.viewTableDataCenterOriginal = [];
    // this.viewTableData = [];
    // this.viewTableDataOriginal = [];
  }

  // changeObjectView(id: any) {
  //   this.selectedObjectView = id;
  // }

  changeLastTaskId(id: any) {
    this.lastTaskIdDistribution = id;
  }

  startRepeat() {
    this.isStatusRepeat = true;
  }

  stopRepeat() {
    this.isStatusRepeat = false;
  }

  resetViewTable() {
    this.viewTableDataCenter = [];
    this.viewTableData = [];
  }

  stopView() {
    runInAction(() => {
      this.timerIdView("Cancel request For View");
    });
    this.isUplodaDataView = false;
  }

  resetObjectLoaded() {
    this.objectLoaded = null;
  }

  async getTable(year: any, month: any, day: any, isModeCenter: boolean) {
    try {
      const { dateTime } = await timeApi.timeController.getTime();
      const currentYear = year ? Number(year) : new Date(dateTime).getFullYear();
      const currentMonth = month ? Number(month) : new Date(dateTime).getMonth();
      const currentDay = day ? Number(day) : new Date(dateTime).getDate();
      const date = prepareDate(String(currentYear), String(currentMonth), String(currentDay));
      if (this.rootStore.authStore.isCenter && isModeCenter) {
        const { items: viewTableData } = await plannedSchedules.plannedSchedulesController.loadedPG(date, this.rootStore.authStore.isCenter);
        const { items: typeList } = await plannedSchedules.plannedSchedulesController.loadType();
        runInAction(() => {
          this.viewTableDataCenter = viewTableData.map((item: any) => {
            const currentDate = new Date(dateTime);
            // const rowColor =
            //   currentYear < currentDate.getFullYear() || currentMonth - 1 < currentDate.getMonth() || currentDay < currentDate.getDate() ? "orange" : "white";

            const rowColor = item?.hasDuplicates
              ? "blue"
              : currentYear < currentDate.getFullYear() || currentMonth - 1 < currentDate.getMonth() || currentDay < currentDate.getDate()
              ? "orange"
              : "white";
            const getType = () => {
              const current = typeList.find((el) => el.code === item.type)?.name ?? item.type;
              if (item.pgNum) {
                if (item.pgNum > 9) {
                  return `${current}-${item.pgNum}`;
                } else {
                  return `${current}-0${item.pgNum}`;
                }
              } else {
                return current;
              }
            };

            const type = getType();

            return {
              ...item,
              tabId: item.pgId,
              type,
              rowColor: rowColor ?? "white",
              oikValue: item?.oik?.status ?? "NO_DATA",
              modesValue: item?.modes?.status ?? "NO_DATA",
              srdkValue: item?.srdk?.status ?? "NO_DATA",
              eg3Value: item?.eg3?.status ?? "NO_DATA",
            };
          });
        });
        runInAction(() => {
          this.viewTableDataCenterOriginal = viewTableData.map((item: any) => {
            const currentDate = new Date(dateTime);
            const rowColor = item?.hasDuplicates
              ? "blue"
              : currentYear < currentDate.getFullYear() || currentMonth - 1 < currentDate.getMonth() || currentDay < currentDate.getDate()
              ? "orange"
              : "white";

            const getType = () => {
              const current = typeList.find((el) => el.code === item.type)?.name ?? item.type;
              if (item.pgNum) {
                if (item.pgNum > 9) {
                  return `${current}-${item.pgNum}`;
                } else {
                  return `${current}-0${item.pgNum}`;
                }
              } else {
                return current;
              }
            };

            const type = getType();

            return {
              ...item,
              tabId: item.pgId,
              type,
              rowColor: rowColor ?? "white",
              oikValue: item?.oik?.status ?? "NO_DATA",
              modesValue: item?.modes?.status ?? "NO_DATA",
              srdkValue: item?.srdk?.status ?? "NO_DATA",
              eg3Value: item?.eg3?.status ?? "NO_DATA",
            };
          });
        });
      } else {
        const { items: viewTableData } = await plannedSchedules.plannedSchedulesController.loadedPG(date, this.rootStore.authStore.isCenter);
        const { items: typeList } = await plannedSchedules.plannedSchedulesController.loadType();
        runInAction(() => {
          this.viewTableData = viewTableData.map((item: any) => {
            const currentDate = new Date(dateTime);
            const rowColor = item?.hasDuplicates
              ? "blue"
              : currentYear < currentDate.getFullYear() || currentMonth - 1 < currentDate.getMonth() || currentDay < currentDate.getDate()
              ? "orange"
              : "white";

            const getType = () => {
              const current = typeList.find((el) => el.code === item.type)?.name ?? item.type;
              if (item.pgNum) {
                if (item.pgNum > 9) {
                  return `${current}-${item.pgNum}`;
                } else {
                  return `${current}-0${item.pgNum}`;
                }
              } else {
                return current;
              }
            };

            const type = getType();

            return {
              ...item,
              tabId: item.pgId,
              type,
              rowColor: rowColor ?? "white",
              oikValue: item?.oik?.status ?? "NO_DATA",
              modesValue: item?.modes?.status ?? "NO_DATA",
              srdkValue: item?.srdk?.status ?? "NO_DATA",
              eg3Value: item?.eg3?.status ?? "NO_DATA",
            };
          });
        });
        runInAction(() => {
          this.viewTableDataOriginal = viewTableData.map((item: any) => {
            const currentDate = new Date(dateTime);
            const rowColor = item?.hasDuplicates
              ? "blue"
              : currentYear < currentDate.getFullYear() || currentMonth - 1 < currentDate.getMonth() || currentDay < currentDate.getDate()
              ? "orange"
              : "white";

            const getType = () => {
              const current = typeList.find((el) => el.code === item.type)?.name ?? item.type;
              if (item.pgNum) {
                if (item.pgNum > 9) {
                  return `${current}-${item.pgNum}`;
                } else {
                  return `${current}-0${item.pgNum}`;
                }
              } else {
                return current;
              }
            };

            const type = getType();

            return {
              ...item,
              tabId: item.pgId,
              type,
              rowColor: rowColor ?? "white",
              oikValue: item?.oik?.status ?? "NO_DATA",
              modesValue: item?.modes?.status ?? "NO_DATA",
              srdkValue: item?.srdk?.status ?? "NO_DATA",
              eg3Value: item?.eg3?.status ?? "NO_DATA",
            };
          });
        });
      }
    } catch (e) {}
  }

  changeObjectView(day: any, month: any, year: any, isCenter: any, objectId: any) {
    this.objectLoaded = { day: String(day), month: String(month), year: String(year), isCenter, objectId: objectId ? objectId : null };
  }

  async getTableViewLoad(year: any, month: any, day: any, selectedObjectId: any, isModeCenter: any) {
    try {
      const { dateTime } = await timeApi.timeController.getTime();
      const currentYear = this?.objectLoaded?.year ? Number(this?.objectLoaded?.year) : new Date(dateTime).getFullYear();
      const currentMonth = this?.objectLoaded?.month ? Number(this?.objectLoaded?.month) : new Date(dateTime).getMonth() + 1;
      const currentDay = this?.objectLoaded?.day ? Number(this?.objectLoaded?.day) : new Date(dateTime).getDate();
      const date = prepareDate(String(currentYear), String(currentMonth), String(currentDay));
      runInAction(() => {
        this.isLoadingListDc = true;
      });
      if (this.rootStore.authStore.isCenter && isModeCenter) {
        const { items: viewTableData } = await plannedSchedules.plannedSchedulesController.loadedPG(date, this.rootStore.authStore.isCenter);
        const { items: typeList } = await plannedSchedules.plannedSchedulesController.loadType();
        runInAction(() => {
          this.viewTableDataCenter = viewTableData.map((item: any) => {
            const currentDate = new Date(dateTime);
            const rowColor = item?.hasDuplicates
              ? "blue"
              : currentYear === currentDate.getFullYear() && currentMonth - 1 === currentDate.getMonth() && currentDay === currentDate.getDate()
              ? "white"
              : "orange";
            // const d1 = new Date(currentYear, currentMonth, currentDay);
            // const d2 = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate());
            // const rowColor = d1 !== d2 ? "orange" : "white";

            const getType = () => {
              const current = typeList.find((el) => el.code === item.type)?.name ?? item.type;
              if (item.pgNum) {
                if (item.pgNum > 9) {
                  return `${current}-${item.pgNum}`;
                } else {
                  return `${current}-0${item.pgNum}`;
                }
              } else {
                return current;
              }
            };

            const type = getType();

            return {
              ...item,
              tabId: item.pgId,
              type,
              rowColor: rowColor ?? "white",
              author: item?.globalAccept?.author ?? (item?.globalAccept?.acceptedAt ? "Автоматический" : ""),
              acceptedAt: item?.globalAccept?.acceptedAt ? prepareDateTable(item?.globalAccept?.acceptedAt) : "",
              oikValue: item?.oik?.status ?? "NO_DATA",
              modesValue: item?.modes?.status ?? "NO_DATA",
              srdkValue: item?.srdk?.status ?? "NO_DATA",
              eg3Value: item?.eg3?.status ?? "NO_DATA",
            };
          });
        });
        runInAction(() => {
          this.viewTableDataCenterOriginal = viewTableData.map((item: any) => {
            const currentDate = new Date(dateTime);
            const rowColor = item?.hasDuplicates
              ? "blue"
              : currentYear === currentDate.getFullYear() && currentMonth - 1 === currentDate.getMonth() && currentDay === currentDate.getDate()
              ? "white"
              : "orange";
            return {
              ...item,
              tabId: item.pgId,
              type: typeList.find((el) => el.code === item.type)?.name ?? item.type,
              rowColor: rowColor ?? "white",
              author: item?.globalAccept?.author ?? (item?.globalAccept?.acceptedAt ? "Автоматический" : ""),
              acceptedAt: item?.globalAccept?.acceptedAt ? prepareDateTable(item?.globalAccept?.acceptedAt) : "",
              oikValue: item?.oik?.status ?? "NO_DATA",
              modesValue: item?.modes?.status ?? "NO_DATA",
              srdkValue: item?.srdk?.status ?? "NO_DATA",
              eg3Value: item?.eg3?.status ?? "NO_DATA",
            };
          });
        });
      } else {
        const { items: viewTableData } = await plannedSchedules.plannedSchedulesController.loadedPG(date, this.rootStore.authStore.isCenter);
        const { items: typeList } = await plannedSchedules.plannedSchedulesController.loadType();
        runInAction(() => {
          this.viewTableData = viewTableData.map((item: any) => {
            const currentDate = new Date(dateTime);
            const rowColor = item?.hasDuplicates
              ? "blue"
              : currentYear === currentDate.getFullYear() && currentMonth - 1 === currentDate.getMonth() && currentDay === currentDate.getDate()
              ? "white"
              : "orange";
            const getType = () => {
              const current = typeList.find((el) => el.code === item.type)?.name ?? item.type;
              if (item.pgNum) {
                if (item.pgNum > 9) {
                  return `${current}-${item.pgNum}`;
                } else {
                  return `${current}-0${item.pgNum}`;
                }
              } else {
                return current;
              }
            };

            const type = getType();
            return {
              ...item,
              tabId: item.pgId,
              type,
              rowColor: rowColor ?? "white",
              oikValue: item?.oik?.status ?? "NO_DATA",
              modesValue: item?.modes?.status ?? "NO_DATA",
              srdkValue: item?.srdk?.status ?? "NO_DATA",
              eg3Value: item?.eg3?.status ?? "NO_DATA",
            };
          });
        });
        runInAction(() => {
          this.viewTableDataOriginal = viewTableData.map((item: any) => {
            const currentDate = new Date(dateTime);
            const rowColor = item?.hasDuplicates
              ? "blue"
              : currentYear === currentDate.getFullYear() && currentMonth - 1 === currentDate.getMonth() && currentDay === currentDate.getDate()
              ? "white"
              : "orange";
            return {
              ...item,
              tabId: item.pgId,
              type: typeList.find((el) => el.code === item.type)?.name ?? item.type,
              rowColor: rowColor ?? "white",
              oikValue: item?.oik?.status ?? "NO_DATA",
              modesValue: item?.modes?.status ?? "NO_DATA",
              srdkValue: item?.srdk?.status ?? "NO_DATA",
              eg3Value: item?.eg3?.status ?? "NO_DATA",
            };
          });
        });
      }
      if (this?.objectLoaded?.objectId) {
        await this.loadInfoForSelected(this?.objectLoaded?.objectId);
      }
      runInAction(() => {
        this.isLoadingListDc = false;
      });
    } catch (e) {}
  }

  async initTableView(year: string, month: string, day: string, isRepeat: boolean, isModeCenter: boolean, selectedObjectId: any) {
    try {
      if (location.pathname === "/planned-schedules") {
        await new Promise(async (resolve, reject) => {
          await this.getTableViewLoad(year, month, day, selectedObjectId, isModeCenter);
          // Если модальное окно со статусом ПГ открыто, обновляем и его данные.
          // Это нужно для того, чтобы данные статуса ПГ в модалке обновлялись
          // вместе со статусами в таблице, чтобы не было рассинхронизации
          if (this.activeStatusModalPgId) {
            await this.getSummaryStatus(this.activeStatusModalPgId);
          }

          setTimeout(resolve, 15000);
          runInAction(() => {
            this.timerIdView = reject;
            this.isUplodaDataView = true;
          });
        });
        if (this.isUplodaDataView) {
          await this.initTableView(year, month, day, isRepeat, isModeCenter, selectedObjectId);
        }
      }
    } catch (e) {}
  }

  async initView(year: string, month: string, day: string, isModeCenter: any) {
    try {
      // runInAction(() => {
      //   this.isLoadingListDc = true;
      // });
      if (isEast) {
        const { items: pbrList } = await plannedSchedules.plannedSchedulesController.loadUDDG(isModeCenter);
        runInAction(() => {
          this.pbrList = pbrList.filter((el) => el.time);
        });
      } else {
        const { items: pbrList } = await plannedSchedules.plannedSchedulesController.loadPBR(isModeCenter);
        runInAction(() => {
          this.pbrList = pbrList;
        });
      }
    } catch (e) {}
  }

  async loadPbr(isModeCenter: any) {
    try {
      if (!this.isStart) {
        this.isStart = true;
        if (isEast) {
          const { items: pbrList } = await plannedSchedules.plannedSchedulesController.loadUDDG(isModeCenter);
          runInAction(() => {
            this.pbrList = pbrList.filter((el) => el.time);
          });
        } else {
          const { items: pbrList } = await plannedSchedules.plannedSchedulesController.loadPBR(isModeCenter);
          runInAction(() => {
            this.pbrList = pbrList;
          });
        }
      }
    } catch (e) {
    } finally {
      this.isStart = false;
    }
  }

  async initPlannedSchedules(year: string, month: string, day: string) {
    try {
      runInAction(() => {
        this.isLoadingPlannedSchedules = true;
      });
      const date = prepareDate(year, month, day);
      const { items } = await plannedSchedules.plannedSchedulesController.viewLoadedPG(date);
      runInAction(() => {
        this.plannedSchedules = items.map((item) => ({ ...item, tabId: item.pgId }));
      });
      return this.plannedSchedules;
    } catch (e) {
    } finally {
      runInAction(() => {
        this.isLoadingPlannedSchedules = false;
      });
    }
  }

  async handleLoadedLater(toLoad: boolean, taskId: string) {
    try {
      const objectPost = { taskId, toLoad };
      await plannedSchedules.plannedSchedulesController.loadedLater(objectPost);

      runInAction(() => {
        this.statusPlanned = this.statusPlanned.map((el) => {
          if (el.taskId === taskId && el.status === "LOADED_LATER") {
            return { ...el, status: "CREATED" };
          }
          return el;
        });
      });
      await this.getStatusPlanned();
    } catch (e) {}
  }

  async alreadyExist(toLoad: boolean, taskId: any) {
    try {
      const objectPost = { taskId, toLoad };
      await plannedSchedules.plannedSchedulesController.alreadyExist(objectPost);
      //
      runInAction(() => {
        this.statusPlanned = this.statusPlanned.map((el) => {
          if (el.status === "ALREADY_EXIST") {
            return { ...el, status: "CREATED" };
          }
          return { ...el };
        });
      });
      //
    } catch (e) {}
  }

  async notAccepted(toLoad: boolean, taskId: any) {
    try {
      const objectPost = { taskId, toLoad };
      await plannedSchedules.plannedSchedulesController.notAccepted(objectPost);
      if (toLoad) {
        // this.rootStore.notificationStore.addNotification({
        //   title: "Сохранение",
        //   description: "Сохранение выполненно",
        //   icon: "save",
        //   type: "done",
        // });
      } else {
        // this.rootStore.notificationStore.addNotification({
        //   title: "Отмена",
        //   description: "Отмена выполненна",
        //   icon: "unavailable",
        //   type: "error",
        // });
        runInAction(() => {
          this.statusPlanned = this.statusPlanned.map((el) => {
            if (el.status === "NOT_ACCEPTED_PG_WITH_LOWER_NUMS") {
              return { ...el, status: "CREATED" };
            }
            return { ...el };
          });
        });
      }
    } catch (e) {}
  }

  async getXmlData(type: string, pgId: string) {
    runInAction(() => {
      this.isLoadingXML = true;
      this.totalXMLLoaded = 0;
    });
    const token = getToken();
    try {
      if (type === "default") {
        this.xhr = new XMLHttpRequest();
        this.xhr.open("GET", `/srpg-center/api/v1/pg/${pgId}/xml`);
        this.xhr.setRequestHeader("Authorization", String(token));
        this.xhr.send();
        this.xhr.onprogress = (e: any) => {
          runInAction(() => {
            this.isLoadingXML = true;
            this.totalXMLLoaded = parseInt(String((e.loaded * 100) / 14224024));
          });
        };

        this.xhr.onload = (e: any) => {
          if (this.xhr.status === 200) {
            this.isLoadingXML = false;
            // @ts-ignore
            saveAs(new Blob([this.xhr.response]), `${this.infoForSelected?.name ?? "undefined"}.xml`);
          }
        };
      }
      if (type === "detail") {
        this.xhr = new XMLHttpRequest();
        this.xhr.open("GET", `/srpg-center/api/v1/pg/${pgId}/detailed-xml`);
        this.xhr.setRequestHeader("Authorization", String(token));
        this.xhr.send();
        this.xhr.onprogress = (e: any) => {
          runInAction(() => {
            this.isLoadingXML = true;
            this.totalXMLLoaded = parseInt(String((e.loaded * 100) / 14790195));
          });
        };

        this.xhr.onload = (e: any) => {
          if (this.xhr.status === 200) {
            this.isLoadingXML = false;
            // @ts-ignore
            saveAs(new Blob([this.xhr.response]), `${this.infoForSelected?.name ?? "undefined"}.xml`);
          }
        };
      }
    } catch (e) {}
  }

  prepareResetData(array: any, result: any[], depIds: any[]) {
    return array.map((item: any) => {
      if (item.childs) {
        this.prepareResetData(item.childs, result, depIds);
      }
      const isFind = depIds.some((el) => el === String(item.tabId));
      if (isFind) {
        result.push(item.controlId);
      }
    });
  }
  async loadFileAll(file: File) {
    try {
      this.isLoadingFile = true;
      const formData = new FormData();
      formData.append("file", file);
      await plannedSchedules.plannedSchedulesController.loadFile(formData, this.rootStore.authStore.isCenter);
      this.rootStore.notificationStore.addNotification({
        title: "Загрузка из файла",
        description: "Задача создана",
        icon: "file",
        type: "done",
      });

      //new
      this.statusPlanned = this.statusPlanned.map((el: any) => {
        if (el.type === "XML") {
          return { ...el, status: "CREATED" };
        }
        return el;
      });
    } catch (e) {
    } finally {
      this.isLoadingFile = false;
      this.getStatusPlanned();
    }
  }

  async resetDistribution(year: any, month: any, day: any, taskId: string, depIds: any[]) {
    try {
      let controlIds: any[] = [];
      this.prepareResetData(this.listDC, controlIds, depIds);
      const { title, message, type, isKeepOnScreen } = await plannedSchedules.plannedSchedulesController.resetDistribution(taskId, controlIds);
      this.getNotification(title, message, type, isKeepOnScreen);
      return true;
    } catch (e) {
      return false;
    }
  }

  async repeatSendAccept(taskId: string, depIds: any[]) {
    try {
      let controlIds: any[] = [];
      this.prepareResetData(this.listDC, controlIds, depIds);
      const { title, message, type, isKeepOnScreen } = await plannedSchedules.plannedSchedulesController.repeatSendAccept(taskId, controlIds);
      this.getNotification(title, message, type, isKeepOnScreen);
      return true;
    } catch (e) {
      return false;
    }
  }

  async sendPGBeforeAccept(pgId: any) {
    try {
      this.isLoadingBeforeAccept = true;
      const { title, message, type, isKeepOnScreen } = await plannedSchedules.plannedSchedulesController.sendPGBeforeAccept(pgId);
      this.getNotification(title, message, type, isKeepOnScreen);
      // this.rootStore.notificationStore.addNotification({
      //   title: "Отправка ПГ (до акцепта)",
      //   description: "Выполнено",
      //   icon: "save",
      //   type: "done",
      // });
      return true;
    } catch (e) {
      return false;
    } finally {
      this.isLoadingBeforeAccept = false;
    }
  }

  async resetAccept(pgId: any, systemType: string) {
    try {
      this.isLoadingResetAccept = true;
      const { title, message, type, isKeepOnScreen } = await plannedSchedules.plannedSchedulesController.resetAccept(pgId, systemType);
      this.getNotification(title, message, type, isKeepOnScreen);
      // this.rootStore.notificationStore.addNotification({
      //   title: "Повтор",
      //   description: "Акцепт перезапущен",
      //   icon: "reset",
      //   type: "information",
      // });
      return true;
    } catch (e) {
      return false;
    } finally {
      this.isLoadingResetAccept = false;
    }
  }

  checkStatus(array: any) {
    return array.some(({ oik, modes, eg3, items }: { oik: any; modes: any; eg3: any; items: any[] }) => {
      let isCheckChilds = false;
      if (items && items.length > 0) {
        isCheckChilds = this.checkStatus(items);
      }
      return (
        (oik?.status && oik?.status === "SENDING_TO_DC") ||
        (oik?.status && oik?.status === "WAITING_FOR_ACCEPT") ||
        (oik?.status && oik?.status === "WAITING_FOR_GLOBAL_ACCEPT") ||
        (oik?.status && oik?.status === "SENDING_GLOBAL_ACCEPT") ||
        (oik?.status && oik?.status === "SENDING_TO_OIK") ||
        (modes?.status && modes?.status === "CREATED") ||
        (eg3?.status && eg3?.status === "CREATED") ||
        isCheckChilds
      );
    });
  }

  startDistributionForm() {
    this.timerDistribution = () => {};
  }

  stopDistribution() {
    this.isProcessing = false;
    this.timerDistribution("Cancel request For Distribution");
  }

  async startCheckingDistribution(year: string, month: string, day: string, lastTaskId: any) {
    try {
      if (location.pathname === "/planned-schedules") {
        await new Promise(async (resolve, reject) => {
          await this.loadDataForSelectedPlannedSchedules(year, month, day, lastTaskId);
          setTimeout(resolve, 15000); //15000
          runInAction(() => {
            this.timerDistribution = reject;
          });
        });
        await this.startCheckingDistribution(year, month, day, lastTaskId);
        // }
      }
    } catch (e) {
    } finally {
      this.isProcessing = false;
    }
  }

  stopCheckStatusDistribution() {
    this.isProcessing = false;
    this.listDC = [];
  }

  resetListDC() {
    runInAction(() => {
      this.listDC = [];
    });
  }

  async saveUnSaveHours(taskId: any, toLoad: boolean) {
    try {
      await plannedSchedules.plannedSchedulesController.saveUnSaveWrongHours(taskId, toLoad);
      if (toLoad) {
        // this.rootStore.notificationStore.addNotification({
        //   title: "Сохранение",
        //   description: "Сохранение выполненно",
        //   icon: "save",
        //   type: "done",
        // });
      } else {
        // this.rootStore.notificationStore.addNotification({
        //   title: "Отмена",
        //   description: "Отмена выполненна",
        //   icon: "unavailable",
        //   type: "error",
        // });
        runInAction(() => {
          this.statusPlanned = this.statusPlanned.map((el) => {
            if (el.status === "WRONG_HOURS_NUM") {
              return { ...el, status: "CREATED" };
            }
            return { ...el };
          });
        });
      }
    } catch (e) {}
  }

  stopStatusPlanned() {
    runInAction(() => {
      this.timerLeftMenu("STOP LEFT MENU");
    });
  }

  async getStatusPlanned() {
    if (location.pathname !== "/planned-schedules") return;

    try {
      clearTimeout(this.timerId);

      const { items: status } = await plannedSchedules.plannedSchedulesController.getStatus();

      runInAction(() => {
        const statusPlanned = isEast
          ? [
              { typeName: "ППБР", type: "DDG" },
              { typeName: "ПБР", type: "UDDG" },
              { typeName: "ПЭР", type: "PER" },
              { typeName: "ПДГ", type: "PDG" },
              { typeName: "Из файла", type: "XML" },
            ]
          : [
              { typeName: "ППБР", type: "PPBR" },
              { typeName: "ПБР", type: "PBR" },
              { typeName: "ПЭР", type: "PER" },
              { typeName: "ПДГ", type: "PDG" },
              { typeName: "Из файла", type: "XML" },
            ];

        this.statusPlanned = statusPlanned.map((item) => {
          const find = status?.find((s: any) => s.type === item.type);
          if (find) {
            return {
              ...item,
              alreadyCreated: find.alreadyCreated,
              notAccepted: find.notAccepted,
              status: find.status,
              user: find.initiatorLogin,
              hourErrors: find.hourErrors,
              taskId: find.taskId,
              pgShortName: find.pgShortName,
              pgActionDate: find.pgActionDate,
              loadedLaterDate: find.loadedLaterDate,
            };
          }
          return item;
        });
      });

      this.timerId = setTimeout(() => this.getStatusPlanned(), 15000);
    } catch (e) {}
  }

  async getSummaryStatus(pgId: string) {
    this.summaryStatusController = new AbortController();
    const signal = this.summaryStatusController.signal;

    try {
      const data = await plannedSchedules.plannedSchedulesController.getSummaryStatus(pgId, { signal });
      // Проверяем, что результат все еще актуален, на случай гонки запросов
      if (!signal.aborted) {
        this.setSummaryStatus(data);
      }
    } catch {
      /* игнор ошибок */
    }
  }

  setSummaryStatus(status: SummaryStatusResponse | null) {
    this.summaryStatus = status;
  }

  openStatusModal = (pgId: string) => {
    // Отменяем текущий запрос, если он есть
    // Может возникнуть, если мы не закрывая модалку, кликаем на другой статус в таблице
    if (this.summaryStatusController) {
      this.summaryStatusController.abort();
    }
    this.activeStatusModalPgId = pgId;
    // Сбрасываем состояние для отображения лоадера
    // Актуально, если мы не закрывая модалку, кликаем на другой статус в таблице
    this.setSummaryStatus(null);
    this.getSummaryStatus(pgId);
  };

  closeStatusModal = () => {
    if (this.summaryStatusController) {
      this.summaryStatusController.abort();
      this.summaryStatusController = null;
    }
    this.activeStatusModalPgId = null;
    this.setSummaryStatus(null);
  };

  async loadDataForSelectedPlannedSchedules(year: string, month: string, day: string, lastTaskId: any) {
    runInAction(() => {
      this.isLoadingListDc = true;
    });
    const taskIdStorage = localStorage.getItem("dist-task-id");
    try {
      if (lastTaskId !== null && lastTaskId === taskIdStorage) {
        const { item, canMakeGlobalAccept, canRepeatGlobalAccept } = await plannedSchedules.plannedSchedulesController.getDataForSelectedPlannedSchedules(lastTaskId);
        runInAction(() => {
          this.listDC = this.prepareAllListDc([item]);
          // ??
          const flatListDc = this.prepareAllListDcFlat([item]).map((el: any) => el.tabId);
          localStorage.setItem("tableOpened-7", JSON.stringify(flatListDc));
          // ??

          sourceDepartments.cancel("request_canceled");
          this.canMakeGlobalAccept = canMakeGlobalAccept ?? false;
          this.canRepeatGlobalAccept = canRepeatGlobalAccept ?? false;
        });
      }
    } catch (e) {
    } finally {
      runInAction(() => {
        this.isLoadingListDc = false;
      });
    }
  }

  prepareDistribution(array: any[], selected: any[]) {
    let result: any[] = [];
    array
      .map((item) => {
        const isHas = selected.some((el) => el === item.tabId);
        if (isHas) {
          result.push(item.tabId);
        } else {
          if (item.childs && item.childs.length > 0) {
            result.push(...this.prepareDistribution(item.childs, selected));
          }
        }
      })
      .filter((item) => item !== null);

    return result;
  }

  async checkAccept(taskId: string) {
    try {
      const { showModal, alreadyAccepted } = await plannedSchedules.plannedSchedulesController.tryAccept(taskId);
      this.alreadyAccepted = alreadyAccepted;
      return showModal;
    } catch (e) {}
  }

  async startDistribution(lastTaskId: any) {
    try {
      const { title, message, type, isKeepOnScreen } = await plannedSchedules.plannedSchedulesController.distributePG(lastTaskId);
      this.getNotification(title, message, type, isKeepOnScreen);
    } catch (e) {}
  }

  async startChecking(year: string, month: string, day: string, pgId: any, isModeCenter: any) {
    try {
      this.isProcessing = true;
      while (this.isProcessing) {
        await this.initView(year, month, day, isModeCenter);
        // await delay(200);

        const processingTasks = this.statusPlanned.some(({ status }) => status === "CREATED");
        if (!processingTasks) {
          this.isProcessing = false;
          await this.startCheckStatusPlanned();
          await this.rootStore.authStore.getNotifications(isModeCenter);
        }
      }
      // if (pgId) {
      //   await this.startCheckingTableView(year, month, day, pgId, false, isModeCenter);
      // }
    } catch (e) {
    } finally {
      this.isProcessing = false;
    }
  }

  async stopCheckStatus() {
    // this.isProcessing = false;
    // const date = prepareDate(String(new Date().getFullYear()), String(new Date().getMonth() + 1), String(new Date().getDate()));
    // const { items: viewTableData } = await plannedSchedules.plannedSchedulesController.loadedPG(date, this.rootStore.authStore.isCenter);
    // const { items: typeList } = await plannedSchedules.plannedSchedulesController.loadType();
    // runInAction(() => {
    //   this.viewTableData = viewTableData.map((item) => ({
    //     ...item,
    //     tabId: item.pgId,
    //     type: typeList.find((el) => el.code === item.type)?.name ?? item.type,
    //   }));
    // });
  }

  stopLoadData() {
    this.loadDataTimer("STOP LOAD DATA");
  }

  async loadData(
    type: any,
    yearCurrent: any,
    monthCurrent: any,
    dayCurrent: any,
    year: any,
    month: any,
    day: any,
    isFilter: boolean,
    pgNum: any,
    pgListForLoadPf: any,
    isModeCenter: any
  ) {
    try {
      if (location.pathname === "/planned-schedules") {
        this.isLoadData = type;
        // await delay(16000); // maybe
        await new Promise(async (resolve, reject) => {
          if (type === "PPBR") {
            const date = new Date(`${yearCurrent}-${monthCurrent}, ${dayCurrent}`);
            if (!isFilter) {
              date.setDate(date.getDate() + 1);
            }
            const finalDate = prepareDate(String(date.getFullYear()), String(date.getMonth() + 1), String(date.getDate()));
            await plannedSchedules.plannedSchedulesController.loadPpbr(finalDate);
            this.rootStore.notificationStore.addNotification({
              title: "Загрузка ППБР",
              description: "Задача создана",
              icon: "start",
              type: "done",
            });
          }
          if (type === "PBR") {
            const date = new Date(`${yearCurrent}-${monthCurrent}, ${dayCurrent}`);
            if (!isFilter) {
              date.setDate(date.getDate());
            }
            //new
            const nextDay = this.pbrList.find((el) => el.number === pgNum)?.nextDay ?? false;
            if (nextDay && !isFilter) {
              date.setDate(date.getDate() + 1);
            }
            //new
            const finalDate = prepareDate(String(date.getFullYear()), String(date.getMonth() + 1), String(date.getDate()));
            await plannedSchedules.plannedSchedulesController.loadPbr(finalDate, pgNum);
            this.rootStore.notificationStore.addNotification({
              title: "Загрузка ПБР",
              description: "Задача создана",
              icon: "start",
              type: "done",
            });
          }
          if (type === "PER") {
            const date = new Date(`${yearCurrent}-${monthCurrent}, ${dayCurrent}`);
            if (!isFilter) {
              date.setDate(date.getDate() + 2);
            }
            const finalDate = prepareDate(String(date.getFullYear()), String(date.getMonth() + 1), String(date.getDate()));
            await plannedSchedules.plannedSchedulesController.loadPer(finalDate);
            this.rootStore.notificationStore.addNotification({
              title: "Загрузка ПЭР",
              description: "Задача создана",
              icon: "start",
              type: "done",
            });
          }
          if (type === "PDG") {
            const date = new Date(`${yearCurrent}-${monthCurrent}, ${dayCurrent}`);
            if (!isFilter) {
              date.setDate(date.getDate() + 1);
            }
            const finalDate = prepareDate(String(date.getFullYear()), String(date.getMonth() + 1), String(date.getDate()));
            await plannedSchedules.plannedSchedulesController.loadPdg(finalDate);
            this.rootStore.notificationStore.addNotification({
              title: "Загрузка ПДГ",
              description: "Задача создана",
              icon: "start",
              type: "done",
            });
          }
          if (type === "DDG") {
            const date = new Date(`${yearCurrent}-${monthCurrent}, ${dayCurrent}`);
            if (!isFilter) {
              date.setDate(date.getDate() + 1);
            }
            const finalDate = prepareDate(String(date.getFullYear()), String(date.getMonth() + 1), String(date.getDate()));
            await plannedSchedules.plannedSchedulesController.loadDdg(finalDate);
            this.rootStore.notificationStore.addNotification({
              title: "Загрузка ППБР",
              description: "Задача создана",
              icon: "start",
              type: "done",
            });
          }
          if (type === "UDDG") {
            const date = new Date(`${yearCurrent}-${monthCurrent}, ${dayCurrent}`);
            if (!isFilter) {
              date.setDate(date.getDate());
            }
            //new
            const nextDay = this.pbrList.find((el) => el.number === pgNum)?.nextDay ?? false;
            if (nextDay && !isFilter) {
              date.setDate(date.getDate() + 1);
            }
            //new
            const finalDate = prepareDate(String(date.getFullYear()), String(date.getMonth() + 1), String(date.getDate()));
            await plannedSchedules.plannedSchedulesController.loadUddg(finalDate, pgNum);
            this.rootStore.notificationStore.addNotification({
              title: "Загрузка ПБР",
              description: "Задача создана",
              icon: "start",
              type: "done",
            });
          }
          await this.getStatusPlanned();
          this.isLoadData = null;
          setTimeout(resolve, 16000); //15000
          runInAction(() => {
            this.loadDataTimer = reject;
          });
        });
      }
      // await this.getTableViewLoad(year, month, day, null);
    } catch (e) {
    } finally {
      this.isLoadData = null;
    }
  }

  async loadInfoForSelected(pgId: string) {
    try {
      this.isLoadingInfo = true;
      if (isModeCenter && this.rootStore.authStore.isCenter) {
        const { endDate, rid, startDate, timestamp, type, name, opam } = await plannedSchedules.plannedSchedulesController.loadDataForSelected(
          pgId,
          this.rootStore.authStore.isCenter
        );
        runInAction(() => {
          this.infoForSelected = { endDate, rid, startDate, timestamp, type, name, opam };
        });
      } else {
        const { items } = await plannedSchedules.plannedSchedulesController.loadType();
        const { item } = await plannedSchedules.plannedSchedulesController.loadDataForSelected(pgId, this.rootStore.authStore.isCenter);
        const type = items.find((el) => el.name === item.type)?.name ?? item.type;
        runInAction(() => {
          this.infoForSelected = { item: { ...item, type: type }, oik: item.oik, modes: item.modes, srdk: item.srdk, eg3: item.eg3 };
        });
      }
    } catch (e) {
    } finally {
      runInAction(() => {
        this.isLoadingInfo = false;
      });
    }
  }

  resetInfoForSelected() {
    try {
      this.infoForSelected = {};
    } catch (e) {}
  }

  async acceptSystems(id: any, isAccepted: any) {
    try {
      this.isAcceptSystem = true;
      await plannedSchedules.plannedSchedulesController.acceptSystems({ id, isAccepted: isAccepted ? isAccepted : false });
      this.rootStore.notificationStore.addNotification({
        title: "Ацепт",
        description: "ПГ Акцептован",
        icon: "done",
        type: "done",
      });
      await delay(20000); // TODO REQUIRED TIME with BACKEND
    } catch (e) {
    } finally {
      this.isAcceptSystem = false;
    }
  }

  async retrySystem(id: any) {
    try {
      this.isLoadingRetry = id;
      const { title, message, type, isKeepOnScreen } = await plannedSchedules.plannedSchedulesController.retrySystem(id);
      this.getNotification(title, message, type, isKeepOnScreen);
      // this.rootStore.notificationStore.addNotification({
      //   title: "Отправка ПГ",
      //   description: "Задача создана",
      //   icon: "success",
      //   type: "done",
      // });
      // await delay(20000); // TODO REQUIRED TIME with BACKEND
    } catch (e) {
    } finally {
      this.isLoadingRetry = null;
    }
  }

  async abortXml() {
    this.isLoadingXML = false;
    this.isLoadingXMLPackage = false;
    if (this?.xhr) {
      this?.xhr?.abort();
    }
  }

  async getXmlDataPackage(type: string, pgId: string) {
    runInAction(() => {
      this.isLoadingXMLPackage = true;
      this.totalXMLLoaded = 0;
    });
    const token = getToken();
    try {
      if (type === "default") {
        this.xhr = new XMLHttpRequest();
        this.xhr.open("GET", `/srpg-control/api/v1/pg/${pgId}/xml`);
        this.xhr.setRequestHeader("Authorization", String(token));
        this.xhr.send();
        this.xhr.onprogress = (e: any) => {
          runInAction(() => {
            this.isLoadingXMLPackage = true;
            this.totalXMLLoaded = parseInt(String((e.loaded * 100) / 14224024));
          });
        };

        this.xhr.onload = (e: any) => {
          if (this.xhr.status === 200) {
            this.isLoadingXMLPackage = false;
            // @ts-ignore
            saveAs(new Blob([this.xhr.response]), `${this.infoForSelected?.item.name ?? "undefined"}.xml`);
          }
        };
      }
      if (type === "detail") {
        this.xhr = new XMLHttpRequest();
        this.xhr.open("GET", `/srpg-control/api/v1/pg/${pgId}/detailed-xml`);
        this.xhr.setRequestHeader("Authorization", String(token));
        this.xhr.send();
        this.xhr.onprogress = (e: any) => {
          runInAction(() => {
            this.isLoadingXML = true;
            this.totalXMLLoaded = parseInt(String((e.loaded * 100) / 14790195));
          });
        };

        this.xhr.onload = (e: any) => {
          if (this.xhr.status === 200) {
            this.isLoadingXML = false;
            this.isLoadingXMLPackage = false;
            // @ts-ignore
            saveAs(new Blob([this.xhr.response]), `${this.infoForSelected?.item.name ?? "undefined"}.xml`);
          }
          if (this.xhr.status === 0) {
            this.isLoadingXML = false;
            this.isLoadingXMLPackage = false;
          }
        };
      }
    } catch (e) {}
  }

  async repeatAccept(pgId: any) {
    try {
      this.isRepeatAccept = true;
      await plannedSchedules.plannedSchedulesController.retryAccept(pgId); // TODO REQUIRED TIME with BACKEND
      this.rootStore.notificationStore.addNotification({
        title: "Повтор",
        description: "Акцептование перезапущено",
        icon: "reset",
        type: "information",
      });
      await delay(20000);
    } catch (e) {
    } finally {
      this.isRepeatAccept = false;
    }
  }

  async sendAccept(pgId: any) {
    try {
      this.isLoadingAccept = true;
      const { title, message, type, isKeepOnScreen } = await plannedSchedules.plannedSchedulesController.sendPreOik(pgId);
      this.getNotification(title, message, type, isKeepOnScreen);
      // this.rootStore.notificationStore.addNotification({
      //   title: "Отправка",
      //   description: "Выполняется отправка ПГ",
      //   icon: "success",
      //   type: "done",
      // });
      // await delay(20000); // TODO REQUIRED TIME with BACKEND
    } catch (e) {
    } finally {
      this.isLoadingAccept = false;
    }
  }

  async initDetailDistribution(controlId: any) {
    try {
      this.isLoadingDetailEvents = true;
      const { events } = await plannedSchedules.plannedSchedulesController.getEventsDepartments(controlId);
      this.eventsDistribution = events.map((el, index) => ({ ...el, tabId: index }));
    } catch (e) {
    } finally {
      this.isLoadingDetailEvents = false;
    }
  }

  reset() {}

  async initDetailModalView(system: any, pgId: any) {
    try {
      const { systemName, pgName, events } = await plannedSchedules.plannedSchedulesController.getExternalSystemEvents(system, pgId);
      this.infoDetailModalViewExternalSystem = { systemName, pgName, events };
    } catch (e) {}
  }

  async fetchAckMessages() {
    try {
      if (location.pathname === "/planned-schedules" && this.rootStore.authStore.isCenter && isModeCenter) {
        const { messages } = await plannedSchedules.plannedSchedulesController.getAckMessages();
        runInAction(() => {
          // Set с id сообщений, которые уже есть на фронтенде
          const existingIds = new Set(this.ackMessages.map((m) => m.id));
          // Оставляем только те сообщения, id которых еще нет в нашем Set'е `existingIds`
          const newMessages = messages.filter((m) => !existingIds.has(m.id));
          // Если после фильтрации остались действительно новые сообщения,
          // добавляем их в конец нашего массива. Если новых сообщений нет,
          // никаких изменений в стейте не происходит, и ре-рендер не запускается.
          if (newMessages.length > 0) {
            this.ackMessages.push(...newMessages);
          }
        });
      }
    } catch (e) {}
  }

  async acknowledgeMessage(messageId: string) {
    try {
      await plannedSchedules.plannedSchedulesController.deleteAckMessage(messageId);
      runInAction(() => {
        this.ackMessages = this.ackMessages.filter((msg) => msg.id !== messageId);
      });
    } catch (e) {}
  }

  startAckPolling() {
    this.stopAckPolling();
    const poll = async () => {
      await this.fetchAckMessages();
      runInAction(() => {
        this.ackTimerId = setTimeout(poll, 5000);
      });
    };
    poll();
  }

  stopAckPolling() {
    if (this.ackTimerId) {
      clearTimeout(this.ackTimerId);
      this.ackTimerId = null;
    }
  }

  clearAckMessages() {
    this.ackMessages = [];
  }
}
